name: build-release
run-name: Build And Release Tabby

on:
  push:
    tags:
      - 'v*'
jobs:
  create_release:
    name: 'Create Release'
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: 'Set Env'
        run: |
          echo "date=$(date --rfc-3339=seconds)" >> ${GITHUB_ENV}
          echo "VERSION=$(./gradlew properties -q | grep "version:" | awk '{print $2}')" >> ${GITHUB_ENV}

      - name: 'Create release'
        uses: actions/create-release@v1
        id: create_release
        with:
          draft: true
          prerelease: false
          release_name: Release ${{ env.VERSION }} ${{ env.date }}
          tag_name: ${{ github.ref_name }}
          body: Release ${{ env.VERSION }} ${{ env.date }}
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - name: 'Write Upload URL To File'
        run: |
          echo "${{steps.create_release.outputs.upload_url}}" > upload_url.txt

      - name: 'Publish Upload URL'
        uses: actions/upload-artifact@v4
        with:
          name: 'upload_url.txt'
          path: 'upload_url.txt'
  build_native_images:
    name: 'Build Native Images'
    needs: [ create_release ]
    strategy:
      matrix:
        #        os: [ 'macos-latest', 'ubuntu-20.04', 'ARM64' ]
        os: [ 'ubuntu-latest' ]
    #        include:
    #          - os: 'ubuntu-20.04'
    #            label: 'linux-amd64'
    #          - os: 'macos-latest'
    #            label: 'mac-amd64'
    #          - os: 'ARM64'
    #            label: 'mac-arm64'
    runs-on: ${{matrix.os}}
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup JDK
        uses: graalvm/setup-graalvm@v1
        with:
          distribution: 'graalvm'
          java-version: '17'
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: 'Get Release URL'
        uses: actions/download-artifact@v4
        with:
          name: 'upload_url.txt'

      - name: 'Get Upload URL'
        run: |
          echo "UPLOAD_URL=$(cat upload_url.txt)" >> ${GITHUB_ENV}

      - name: build
        run: |
          ./gradlew bootJar

      - name: 'Publish Native Image'
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: 'tabby.jar'
          path: 'build/libs/tabby.jar'

      - name: 'Release Native Image Asset'
        if: success()
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        with:
          upload_url: ${{env.UPLOAD_URL}}
          asset_name: 'tabby.jar'
          asset_path: 'build/libs/tabby.jar'
          asset_content_type: application/octet-stream