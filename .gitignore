HELP.md
.gradle
build/

!**/src/main/**/build/
!**/src/test/**/build/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

temp/
*.class
.idea/
cases/

.mvn/
mvnw*
logs/
cache/*
lib/*

runtime.json
*.csv
*.db
ignores.json
cql.txt
*.jar

.DS_Store
*.tar.gz
config/db.properties
rules/cyphers.yml
target/
release
**/src/test/**
output
env