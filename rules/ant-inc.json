[{"name": "com.alibank.mobiledsp.mobileservice.invoke.MobileRpcHolder", "rules": [{"function": "getRoleId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getIpId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}]}, {"name": "com.alipay.mobilegw.mobileservice.invoke.MobileRpcHolder", "rules": [{"function": "getLiteSession", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getSession", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}]}, {"name": "com.alipay.fc.common.lang.event.EventContext", "rules": [{"function": "getIpRoleId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getIpId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getOperator", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}]}, {"name": "javax.servlet.http.HttpSession", "rules": [{"function": "getAttribute", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}]}, {"name": "com.mybank.bkpaygw.spi.adapter.adapterservice.model.PayGwPrincipal", "rules": [{"function": "getIpId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getIpRoleId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getAlipayUserId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}]}, {"name": "com.mybank.bkecgw.common.service.facade.model.BkecgwLoginContext", "rules": [{"function": "getIpId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getIpRoleId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getAlipayUid", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}, {"function": "getBkecgwAuthor", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}]}, {"name": "com.alipay.xauth.web.XAuthSessionHolder", "rules": [{"function": "getLoginUserId", "type": "know", "vul": "login", "actions": {"return<s>": ["auth"]}, "polluted": [], "max": -2}]}]