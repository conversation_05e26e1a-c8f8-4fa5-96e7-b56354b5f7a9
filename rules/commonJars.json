["commons-", "poi-", "jetty-", "apache-el", "apache-jsp", "apache-jstl", "apache-ant", "mybatis-", "asm-", "mysql-connector-java", "netty-", "dubbo-", "el-api-", "<PERSON><PERSON><PERSON>-", "guava-", "httpclient-", "httpcore-", "ibatis-sqlmap", "itext-", "jackson-", "servlet-api", "javassist-", "spring-", "javax.servlet.jsp.jstl-api", "javax.ws.rs-api", "javax.annotation-api", "crypto-", "ognl-", "logback-", "taglibs-standard-", "zookeeper-", "jfreechart-", "jline-", "slf4j-api", "com.springsource.", "abatis-sqlmap", "ojdbc", "kryo-", "guice-", "xstream-", "struts", "xml-", "log4j-", "<PERSON>jwe<PERSON>-", "org.eclipse", "jboss-", "hibernate-", "castor-", "quartz-", "javax.activation-", "junit-", "jsp-api", "jaxb-", "org.codehaus.groovy", "groovy", "json-", "druid-", "confkeeper-", "aopalliance-", "jdom-", "validation-api", "javax.servlet-", "jconsole-", "xpp3_min", "toolkit-common-", "hessian-", "antlr", "axis2-", "gson-", "log4j-", "jersey-", "snakeyaml-", "jakarta.annotation-", "lombok-", "xmlbeans-", "velocity-", "javax.servlet", "freemarker-", "slf4j-", "dom4j-", "c3p0-", "standard-", "bsh-", "jstl-", "tomcat-", "jul-to-", "jcl-to-", "h2-", "grpc-", "java-jwt", "okhttp-", "annotations-", "protobuf-", "jedis-", "lucene-", "reflections-", "htmlparser", "xbean-", "javax.mail", "xmlsec-", "java-uuid", "xerces-", "svg-", "joda-", "easy-okhttp", "opensaml-", "aspect", "cglib-", "xalan", "org.apache.", "ant.", "graph-java-", "mvel2-", "jdom", "google-oauth-", "postgresql-", "activemq-", "flex-", "swagger-", "batik-", "batik-", "ehcache-", "cssparser-", "HikariCP-", "pdfbox-", "ibatis-", "rhino-", "dom4j.", "itextpdf-", "jna-", "hutool-", "aliyun-java-", "javassist.", "ooxml-", "oracle.", "quartz.", "google-", "jakarta-", "dom.", "org.restlet-", "bcprov-", "xerces.", "log4j.", "forms-", "jni.", "jmx.", "mail-", "activation", "gson-", "tomcat-", "healthcheck-", "mist-", "mvc-", "notify-", "pagehelper-", "tracer-", "font-asian", "ecj-", "acegi-", "cxf-", "proto-", "ant-1", "ant-2", "ant-bouncy-", "resteasy-jaxrs-", "schema-sync-connector-", "caffeine-", "bcpkix-", "layotto-", "lz4-", "micrometer-core", "avro-", "aliyun-log", "javers-", "network.core", "woodstox-", "sketch-jar", "jsqlparser-", "antq-", "xmemcached", "layout-", "aviator-", "classgraph-", "jctools-", "antlr4-", "lookout", "hadoop-", "bcprov-", "dingding-", "fontbox-", "flying-", "jersey-guava-", "ssm-", "xmlbeans-", "antdld-", "mvel2-", "doom-", "quartz-", "snappy-", "rxjava-", "eagleeye-", "jedis-", "lettuce-", "spymemcached-", "org.apache", "mina-", "org.springframework", "freemarker-", "ats-", "wsdl4j-", "acapi-", "xts-", "lucene-", "rocketmq-", "velocity-", "smartmw-", "jakarta.commons.", "javax.", "osgi-resource", "afts-", "logstat-", "elasticsearch-", "jsoup", "XmlSchema", "servo-core", "script.juel", "rdslib-common", "smonitor", "logger", "mail", "network.service", "network-util", "jersey-", "umid-", "mockito-", "ibatis2-", "jdbc.mysql", "antlr", "xmlschema-", "axis-", "http-agent-", "smtp-", "jcl-", "xmlpull-", "reactor-", "websocket-", "jakarta-", "jmock-", "taglibs-", "opensaml-"]