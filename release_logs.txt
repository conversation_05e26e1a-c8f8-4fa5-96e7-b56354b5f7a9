2025-09-03 优化并发冲突等待的逻辑，增加配置tabby.build.thread.timeout、tabby.build.thread.waiting.level。经实践证明waiting.level可以尝试多加一些，thread.timeout维持在1/2的method.timeout，性能相对较好。具体可以在实际电脑上测试后确定
2025-07-06 修复copy函数cache影响结果的bug；修复一些解析错误；
2025-06-09 增加summaries大小限制，防止因为大summaries的还原影响分析性能
2025-05-06 实现了新的污点引擎，第一版后续需要测试找出bug
2024-09-30 修复action处理bug
2024-09-11 优化停止逻辑
2024-03-23 修复 csv 导入解析错误
2024-03-22 修改mybatis的解析器
2023-09-26 修复 Call 边上 IS_CALLER_THIS_FIELD_OBJ 逻辑
2023-11-09 修复 receiveBody 多线程错误；调整程序代码结构；修复数据库获取bug
2023-11-21 优化 url 获取逻辑