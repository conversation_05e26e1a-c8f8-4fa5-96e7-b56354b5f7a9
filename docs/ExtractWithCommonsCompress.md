# 使用 Commons-Compress 库的 JAR 文件提取功能

## 概述

为了解决原有 `extract` 函数在处理某些 JAR 文件时出现 "zip end header not found" 错误的问题，我们新增了基于 Apache Commons-Compress 库的 `extractWithCommonsCompress` 函数。

## 主要改进

### 1. 更好的容错能力
- 使用 Apache Commons-Compress 库，对损坏的 ZIP 文件头有更好的容错能力
- 多重回退机制：标准方法 → Commons-Compress → CompressorStreamFactory → 宽松模式
- 详细的错误日志，便于问题诊断

### 2. 保持向后兼容
- 原有的 `extract` 方法保持不变
- 新增的 `extractWithCommonsCompress` 方法具有相同的接口
- `unpack` 方法默认使用新的提取方法，但提供选项切换回原方法

## 新增方法

### extractWithCommonsCompress
```java
public static void extractWithCommonsCompress(Path jarPath, Path tmpDir, List<String> suffixes) throws IOException
```

**参数说明：**
- `jarPath`: JAR/ZIP 文件路径
- `tmpDir`: 目标解压目录
- `suffixes`: 需要提取的文件后缀列表（如 `.class`, `.xml`, `.jar` 等）

**特性：**
- 首先尝试使用标准 `ZipFile` 方法
- 如果失败，自动切换到 Commons-Compress
- 支持多种压缩格式检测
- 提供宽松模式处理严重损坏的文件

### unpack 方法重载
```java
// 默认使用 commons-compress（推荐）
public static Path unpack(Path path, String filename) throws IOException

// 可选择提取方法
public static Path unpack(Path path, String filename, boolean useCommonsCompress) throws IOException
```

## 使用示例

### 基本使用
```java
import tabby.common.utils.FileUtils;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;

// 提取 JAR 文件
Path jarPath = Paths.get("/path/to/your.jar");
Path outputDir = Paths.get("/tmp/extracted");
List<String> suffixes = Arrays.asList(".class", ".xml", ".jar");

try {
    FileUtils.extractWithCommonsCompress(jarPath, outputDir, suffixes);
    System.out.println("提取成功！");
} catch (IOException e) {
    System.err.println("提取失败: " + e.getMessage());
}
```

### 使用 unpack 方法
```java
// 使用默认的 commons-compress 方法
Path tempDir = FileUtils.unpack(jarPath, "myapp");

// 或者选择使用标准方法
Path tempDir2 = FileUtils.unpack(jarPath, "myapp", false);
```

### 处理损坏的 JAR 文件
```java
public void handlePotentiallyCorruptedJar(String jarFilePath) {
    try {
        Path jarPath = Paths.get(jarFilePath);
        Path outputDir = Paths.get("/tmp/extract");
        List<String> suffixes = Arrays.asList(".class", ".jar", ".xml");
        
        FileUtils.extractWithCommonsCompress(jarPath, outputDir, suffixes);
        log.info("成功提取可能损坏的 JAR 文件");
        
    } catch (IOException e) {
        log.error("无法提取 JAR 文件: {}", e.getMessage());
        // 可以在这里添加额外的错误处理逻辑
    }
}
```

## 错误处理机制

新的提取方法采用多层回退策略：

1. **第一层**：尝试使用标准 `ZipFile` 方法
2. **第二层**：使用 `JarArchiveInputStream` 或 `ZipArchiveInputStream`
3. **第三层**：使用 `CompressorStreamFactory` 进行格式检测
4. **第四层**：使用宽松模式的 `ZipArchiveInputStream`

每一层失败都会记录详细的日志信息，便于问题诊断。

## 性能考虑

- 对于正常的 JAR 文件，首先使用标准方法，性能与原方法相同
- 只有在标准方法失败时才使用 Commons-Compress，避免不必要的性能开销
- Commons-Compress 方法虽然更健壮，但可能略慢于标准方法

## 日志输出

新方法提供详细的日志输出：

```
DEBUG - Successfully extracted /path/to/normal.jar using standard ZipFile
WARN  - Standard ZipFile extraction failed for /path/to/corrupted.jar, trying commons-compress: zip END header not found
INFO  - Successfully extracted /path/to/corrupted.jar using commons-compress
```

## 依赖要求

确保项目中包含 Apache Commons-Compress 依赖：

```xml
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-compress</artifactId>
    <version>1.27.1</version>
</dependency>
```

## 迁移建议

### 对于新代码
推荐直接使用 `extractWithCommonsCompress` 方法或默认的 `unpack` 方法。

### 对于现有代码
- 现有的 `extract` 方法调用无需修改，继续正常工作
- 如果遇到 "zip end header not found" 错误，可以替换为 `extractWithCommonsCompress`
- `unpack` 方法已自动升级为使用新的提取方法

## 测试

项目包含完整的单元测试 `FileUtilsExtractTest`，覆盖：
- 正常 JAR 文件提取
- 损坏 JAR 文件处理
- 不同提取方法的对比
- 边界条件测试

运行测试：
```bash
mvn test -Dtest=FileUtilsExtractTest
```

## 故障排除

### 常见问题

1. **"zip END header not found"**
   - 这是原问题，新方法已解决
   - 如果仍然出现，检查文件是否严重损坏

2. **"Cannot find zip signature within the file"**
   - 文件可能不是有效的 ZIP/JAR 格式
   - 检查文件完整性

3. **内存不足**
   - 对于大型 JAR 文件，确保有足够的堆内存
   - 考虑增加 JVM 内存参数

### 调试建议

1. 启用详细日志：设置日志级别为 DEBUG
2. 检查文件权限和磁盘空间
3. 验证 JAR 文件的完整性
4. 如果问题持续，可以尝试使用标准方法作为回退

## 示例程序

项目包含完整的示例程序 `ExtractExample.java`，演示：
- 基本提取功能
- 错误处理
- 批量处理
- 不同方法对比

运行示例：
```bash
java -cp target/classes tabby.example.ExtractExample /path/to/your.jar /tmp/extract
```
