package tabby.neo4j.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tabby.config.GlobalConfiguration;
import tabby.neo4j.repository.MethodRefRepository;
import tabby.common.utils.FileUtils;

/**
 * <AUTHOR>
 * @since 2021/3/29
 */
@Slf4j
@Service
public class MethodService {

    @Autowired
    private MethodRefRepository methodRefRepository;

    public void importMethodRef(){
        log.info("Save Method Node");
        methodRefRepository.loadMethodRefFromCSV(
                FileUtils.getWinPath(GlobalConfiguration.METHODS_CACHE_PATH));
    }

    public MethodRefRepository getRepository(){
        return methodRefRepository;
    }
}
