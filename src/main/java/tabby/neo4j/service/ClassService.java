package tabby.neo4j.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tabby.neo4j.repository.MethodRefRepository;
import tabby.neo4j.repository.ClassRefRepository;
import tabby.common.utils.FileUtils;
import tabby.config.GlobalConfiguration;

/**
 * <AUTHOR>
 * @since 2021/3/29
 */
@Slf4j
@Service
public class ClassService {

    @Autowired
    private ClassRefRepository classRefRepository;
    @Autowired
    private MethodRefRepository methodRefRepository;


    public void clear(){
        log.info("Start to clean old data.");
        classRefRepository.deleteAllData();
        log.info("Clean old data. Done!");
    }

    public void statistic(){
        int countAllNode = classRefRepository.countAllNode();
        int countAllEdge = classRefRepository.countAllEdge();
        log.info("Load {} nodes, {} edges.", countAllNode, countAllEdge);
    }

    public void importClassRef(){
        log.info("Save Class Node");
        classRefRepository.loadClassRefFromCSV(
                FileUtils.getWinPath(GlobalConfiguration.CLASSES_CACHE_PATH));
    }
    public void buildEdge(){
        long extendCount = classRefRepository.loadExtendEdgeFromCSV(
                FileUtils.getWinPath(GlobalConfiguration.EXTEND_RELATIONSHIP_CACHE_PATH));
        log.info("Load Extend relationship count: {}", extendCount);
        long interfacesCount = classRefRepository.loadInterfacesEdgeFromCSV(
                FileUtils.getWinPath(GlobalConfiguration.INTERFACE_RELATIONSHIP_CACHE_PATH));
        log.info("Load Interface relationship count: {}", interfacesCount);

        long hasCount = classRefRepository.loadHasEdgeFromCSV(
                FileUtils.getWinPath(GlobalConfiguration.HAS_RELATIONSHIP_CACHE_PATH));
        log.info("Load has relationship count: {}", hasCount);

        long callCount = methodRefRepository.loadCallEdgeFromCSV(
                FileUtils.getWinPath(GlobalConfiguration.CALL_RELATIONSHIP_CACHE_PATH));
        log.info("Load call relationship count: {}", callCount);

        long aliasCount = methodRefRepository.loadAliasEdgeFromCSV(
                FileUtils.getWinPath(GlobalConfiguration.ALIAS_RELATIONSHIP_CACHE_PATH));
        log.info("Load alias relationship count: {}", aliasCount);
    }

}
