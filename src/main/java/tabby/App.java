package tabby;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import com.beust.jcommander.JCommander;
import com.beust.jcommander.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import tabby.analysis.CachedPointerAnalysis;
import tabby.common.utils.FileUtils;
import tabby.config.GlobalConfiguration;
import tabby.core.Analyser;
import tabby.neo4j.Loader;

@Slf4j
@SpringBootApplication
@EnableScheduling
@EntityScan({"tabby.common.bean"})
public class App {

    @Autowired
    private Analyser analyser;

    @Autowired
    private Loader loader;

    public static boolean isDone = false;

    public static void main(String[] args) {
        Command command = new Command();
        JCommander.newBuilder()
                .addObject(command)
                .build()
                .parse(args);

        if(command.configFilePath != null){
            GlobalConfiguration.CONFIG_FILE_PATH = FileUtils.getRealPath(command.configFilePath);
        }

        if(command.isLoad()){
            GlobalConfiguration.IS_LOAD = true;
            GlobalConfiguration.CSV_PATH = command.csvFilePath;
            GlobalConfiguration.initLoadConfig();
        }else{
            GlobalConfiguration.init();
        }

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if(!App.isDone){
                log.error("Force Stop by control+c");
                CachedPointerAnalysis.stopAll();
                stopThreads(GlobalConfiguration.tabbyCollectorExecutor);
                stopThreads(GlobalConfiguration.tabbySaverExecutor);
            }
        }));

        SpringApplication.run(App.class, args).close();
    }

    public void setJavaHome() {
        // set java home
        if (GlobalConfiguration.TARGET_JAVA_HOME == null || !GlobalConfiguration.IS_USING_SETTING_JRE) {
            String javaHome = System.getProperty("java.home");
            if (javaHome == null) {
                javaHome = System.getenv("JAVA_HOME");
            }
            if (javaHome != null) {
                GlobalConfiguration.TARGET_JAVA_HOME = javaHome;
            }
        }

        log.info("Target java.home: " + GlobalConfiguration.TARGET_JAVA_HOME);
    }

    public void setLogDebugLevel() {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        if (GlobalConfiguration.DEBUG) {
            loggerContext.getLogger("tabby").setLevel(Level.DEBUG);
        }
    }

    @Bean
    CommandLineRunner run() {
        return args -> {
            if(GlobalConfiguration.IS_LOAD){
                loader.load();
            }else{
                try {
                    GlobalConfiguration.initConfig();
                    setJavaHome();
                    setLogDebugLevel();
                    analyser.run();
                } catch (IllegalArgumentException e) {
                    log.error(e.getMessage() + ", Please check your settings.properties file.");
                }
            }

            isDone = true;
            if (GlobalConfiguration.GLOBAL_FORCE_STOP) {
                log.info("OOM ERROR!");
            } else {
                log.info("Done. Bye!");
            }
        };
    }

    public static void stopThreads(ThreadPoolTaskExecutor executor) {
        if (executor != null && executor.getActiveCount() > 0) {
            executor.shutdown();
        }
    }


    static final class Command {
        @Parameter(names={"--config"})
        public String configFilePath = null;

        @Parameter(names={"--load", "-l"})
        public String csvFilePath = null;

        public boolean isLoad(){
            return csvFilePath != null;
        }
    }
}
