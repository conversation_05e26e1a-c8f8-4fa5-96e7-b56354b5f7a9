package tabby.core.collector;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import soot.SootMethod;
import tabby.analysis.CachedPointerAnalysis;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.TickTock;
import tabby.config.GlobalConfiguration;
import tabby.core.container.DataContainer;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021/4/23
 */
@Slf4j
@Service
@Setter
public class CallGraphCollector {

    public CallGraphCollector() {
    }

    /**
     * 全量调用图生成
     */
    @Async("tabby-collector")
    public void collect(MethodReference methodRef, DataContainer dataContainer, TickTock tickTock) {
        String uuid = UUID.randomUUID().toString();
        try {
            SootMethod method = methodRef.getMethod();
            if (method == null) {
                tickTock.countDown();
                return; // 提取不出内容，不分析
            }

//            if(!methodRef.getSignature().equals("<java.util.regex.Pattern: java.util.regex.Pattern$Node expr(java.util.regex.Pattern$Node)>")) {
//                tickTock.countDown();
//                return;
//            }

            log.debug(method.getSignature()+" start...");
            long start = System.nanoTime();
            CachedPointerAnalysis.analyse(uuid, method, methodRef, dataContainer);
            log.debug(method.getSignature()+" end, cost: "+(TimeUnit.NANOSECONDS.toSeconds(System.nanoTime() - start))+" s.");
            tickTock.countDown(); // TODO debug
        } catch (RuntimeException e) {
            log.error("Something error on call graph. " + methodRef.getSignature());
            String msg = e.getMessage();
            log.error(msg);
            tickTock.countDown();
        } catch (OutOfMemoryError e) {
            log.error("OOM Error!!!! Force Stop Everything!!!");
            System.exit(1); // just stop
            tickTock.countDown();
            GlobalConfiguration.GLOBAL_FORCE_STOP = true;
        } catch (Exception e) {
            tickTock.countDown();
            if (e instanceof InterruptedException) {
                log.error("Thread interrupted. " + methodRef.getSignature());
                return;
            } else {
                log.error("Something error on call graph. " + methodRef.getSignature());
                e.printStackTrace();
            }
        } finally {
            CachedPointerAnalysis.runningMethods.remove(uuid);
        }

        log.debug("Remain {} methods.", tickTock.getCount());
    }

}
