package tabby.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/2/8
 */
@Slf4j
public class PositionUtils {

    public final static int THIS = -1;
    public final static int SOURCE = -2;
    public final static int NOT_POLLUTED_POSITION = -3;
    public final static int DAO = -4;
    public final static int RPC = -5;
    public final static int AUTH = -6;
    public final static int NULL_TYPE = -7;

    public final static String THIS_STRING = "this";
    public final static String SOURCE_STRING = "source";
    public final static String DAO_STRING = "dao";
    public final static String RPC_STRING = "rpc";
    public final static String AUTH_STRING = "auth";
    public final static String PARAM_STRING = "param";
    public final static String NULL_STRING = "null_type";
    public final static String UNTAITED_STRING = "untainted";

    public static String getPosition(int pos) {
        if (THIS == pos) {
            return THIS_STRING;
        } else if (SOURCE == pos) {
            return SOURCE_STRING; // source暂时不用处理
        } else if (pos >= 0) {
            return PARAM_STRING + "-" +pos;
        } else if (DAO == pos) {
            return DAO_STRING;
        } else if (RPC == pos) {
            return RPC_STRING;
        } else if (AUTH == pos) {
            return AUTH_STRING;
        } else if(NOT_POLLUTED_POSITION == pos){
            return UNTAITED_STRING;
        } else if(NULL_TYPE == pos){
            return NULL_STRING;
        } else {
            return null;
        }
    }

    public static int getPosition(String pos) {
        if (pos == null || pos.isEmpty()) return NOT_POLLUTED_POSITION;
        if (pos.startsWith(THIS_STRING)) {
            return THIS;
        } else if (pos.startsWith(PARAM_STRING+"-")) {
            return extractParamNumber(pos);
        } else if (pos.startsWith(SOURCE_STRING)) {
            return SOURCE;
        } else if (pos.startsWith(DAO_STRING)) {
            return DAO;
        } else if (pos.startsWith(RPC_STRING)) {
            return RPC;
        } else if (pos.startsWith(AUTH_STRING)) {
            return AUTH;
        } else if(pos.equals(NULL_STRING)){
            return NULL_TYPE;
        } else if(pos.equals(UNTAITED_STRING)){
            return NOT_POLLUTED_POSITION;
        }
        return NOT_POLLUTED_POSITION;
    }

    /**
     * 从畸形的 param 字符串中提取参数编号
     * 处理类似 "param-1<f<s>", "param-2<a><k>", "param-0<f>" 等情况
     *
     * @param pos 原始位置字符串
     * @return 参数编号，如果解析失败返回 NOT_POLLUTED_POSITION
     */
    private static int extractParamNumber(String pos) {
        try {
            // 首先检查是否包含范围标记 ".."
            if (pos.contains("..")) {
                return NOT_POLLUTED_POSITION;
            }

            // 使用正则表达式匹配 param-数字 模式
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^param-(\\d+)");
            java.util.regex.Matcher matcher = pattern.matcher(pos);

            if (matcher.find()) {
                String numberStr = matcher.group(1);
                int paramNumber = Integer.parseInt(numberStr);
                log.debug("Successfully extracted param number {} from: {}", paramNumber, pos);
                return paramNumber;
            } else {
                // 如果正则匹配失败，尝试传统方法作为备用
                return extractParamNumberFallback(pos);
            }
        } catch (Exception e) {
            log.warn("Failed to extract param number from: {} - {}", pos, e.getMessage());
            return NOT_POLLUTED_POSITION;
        }
    }

    /**
     * 备用的参数编号提取方法，使用传统的字符串处理
     */
    private static int extractParamNumberFallback(String pos) {
        try {
            String realPos = pos;

            // 处理各种标签，按照可能出现的顺序处理
            if (realPos.contains("<f>")) {
                realPos = realPos.split("<f>")[0];
            }

            // 清理其他标签
            realPos = SemanticUtils.cleanTag(realPos, "<s>", "<a>", "<k>", "<v>", "<f", "<");

            // 分割字符串
            String[] data = realPos.split("-");

            if (data.length >= 2) {
                String numberPart = data[1];

                // 进一步清理可能残留的特殊字符，但保留数字和点
                numberPart = numberPart.replaceAll("[^0-9.]", "");

                if (numberPart.contains("..")) {
                    return NOT_POLLUTED_POSITION;
                }

                // 如果包含单个小数点，只取整数部分
                if (numberPart.contains(".")) {
                    String[] parts = numberPart.split("\\.");
                    if (parts.length > 0 && !parts[0].isEmpty()) {
                        numberPart = parts[0];
                    } else {
                        return NOT_POLLUTED_POSITION;
                    }
                }

                if (!numberPart.isEmpty()) {
                    return Integer.parseInt(numberPart);
                }
            }

            log.warn("Could not extract param number from: {}", pos);
            return NOT_POLLUTED_POSITION;
        } catch (Exception e) {
            log.error("Fallback parse error for: {} - {}", pos, e.getMessage());
            return NOT_POLLUTED_POSITION;
        }
    }

    public static boolean isSpecialTag(String tag) {
        return SOURCE_STRING.equals(tag) ||
                DAO_STRING.equals(tag) ||
                AUTH_STRING.equals(tag) ||
                RPC_STRING.equals(tag);
    }

    public static boolean isPositionTag(String tag) {
        int pos = getPosition(tag);
        return pos != NOT_POLLUTED_POSITION || "return".equals(tag);
    }

    public static boolean isTaintFreeTag(String tag) {
        if(tag == null || tag.isEmpty()) return true;
        if(tag.contains("<f>") || tag.contains("<k>") || tag.contains("<v>") || tag.contains("<a>")) return false;
        tag = SemanticUtils.cleanTag(tag, "<s>");
        return THIS_STRING.equals(tag) || tag.startsWith(PARAM_STRING + "-");
    }

    public static int[] getMultiParamPositions(String pos) {
        int[] ret = new int[2];
        String realPos = pos;
        if (pos.contains("<a>")) {
            realPos = realPos.replace("<a>", "");
        }

        if (realPos.contains("<f>")) {
            realPos = realPos.split("<f>")[0];
        }

        String[] data = realPos.split("-");
        String[] startAndEnd = data[1].split("\\.\\.");
        ret[0] = Integer.parseInt(startAndEnd[0]);
        if ("n".equals(startAndEnd[1])) {
            ret[1] = THIS;
        } else {
            ret[1] = Integer.parseInt(startAndEnd[1]);
        }
        return ret;
    }

    public static Set<String> getPositions(Set<Integer> positions, int exclude) {
        Set<String> pos = new HashSet<>();
        for (int p : positions) {
            if (p == exclude) continue;
            String tmp = getPosition(p);
            if (tmp != null) {
                pos.add(tmp);
            }
        }
        return pos;
    }

    public static Set<String> getPositions(Set<Integer> positions, Collection<String> table) {
        Set<String> pos = new HashSet<>();
        for (int p : positions) {
            String tmp = getPosition(p);
            if (tmp != null && table.contains(tmp)) {
                pos.add(tmp);
            }
        }
        return pos;
    }

    public static Set<Integer> getPositions(Set<String> positions) {
        Set<Integer> retData = new HashSet<>();
        if (positions != null) {
            for (String pos : positions) {
                retData.add(getPosition(pos));
            }
            retData.remove(NOT_POLLUTED_POSITION); // 剔除没用的-3
        }
        if (retData.isEmpty()) {
            retData.add(NOT_POLLUTED_POSITION);
        }
        return retData;
    }

    public static boolean isSpecialAction(String action) {
        return action.startsWith(AUTH_STRING) || action.startsWith(SOURCE_STRING) || action.startsWith(DAO_STRING) || action.startsWith(RPC_STRING);
    }
}