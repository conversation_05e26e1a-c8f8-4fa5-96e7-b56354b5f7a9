package tabby.common.bean.ref;

import com.google.common.base.Objects;
import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import com.google.common.collect.Multimaps;
import com.google.common.collect.SetMultimap;
import com.google.common.hash.Hashing;
import jakarta.persistence.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import soot.Body;
import soot.SootClass;
import soot.SootMethod;
import tabby.analysis.common.enums.AnalysisState;
import tabby.common.bean.converter.*;
import tabby.common.bean.edge.Alias;
import tabby.common.bean.edge.Call;
import tabby.common.utils.SemanticUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @since 2020/10/9
 */
@Data
@Entity
@Slf4j
@Table(name = "methods")
public class MethodReference {

    // method 基础字段
    @Id
    private String id;
    private String name;
    @Column(columnDefinition = "TEXT")
    private String signature;
    @Column(columnDefinition = "TEXT")
    private String subSignature;
    @Column(columnDefinition = "TEXT")
    private String name0;
    @Column(columnDefinition = "TEXT")
    @Convert(converter = Set2JsonStringConverter.class)
    private Set<String> returnType = new HashSet<>();
    private boolean isReturnConstantType = false;
    private String type;
    private int modifiers;
    private String classname;
    private int parameterSize;
    private String vul;
    private String urlPath;
    @Column(columnDefinition = "TEXT")
    @Convert(converter = Map2JsonStringForAnnotationsConverter.class)
    private Map<String, Map<String, Set<String>>> annotations = new HashMap<>();
    private boolean isSink = false;
    private boolean isIgnore = false;
    private boolean isStatic = false;
    private boolean isPublic = false;
    private boolean hasParameters = false;
    private boolean hasDefaultConstructor = false; // 继承自class
    private boolean isSerializable = false;
    private boolean isAbstract = false;
    private boolean isEndpoint = false;
    private boolean isNettyEndpoint = false;
    private boolean isContainsOutOfMemOptions = false;
    private boolean isActionContainsSwap = false;
    private boolean isGetter = false;
    private boolean isSetter = false;
    private boolean isFromAbstractClass = false;
    private boolean isNative = false;
    private boolean isPhantom = false;

    /**
     * 污染传递点，主要标记2种类型，this和param
     * 其他函数可以依靠relatedPosition，来判断当前位置是否是通路
     * old=new
     * param-0=other value
     * param-0=param-1,param-0=this.field
     * return=other value
     * return=param-0,return=this.field
     * this.field=other value
     * 提示经过当前函数调用后，当前函数参数和返回值的relateType会发生如下变化
     *
     * actions 仅来源于规则或动态规则，非规则引入的缓存使用 summaries 字段
     * actions 分析过程中是不可变的
     * 此外，规则当中，应该避免使用多层结构。
     * 只允许出现 return/this/param-n，一层<f> 临时变量用<s>
     */
    @Column(columnDefinition = "TEXT")
    @Convert(converter = MultiMap2JsonStringConverter.class)
    private SetMultimap<String, String> actions = MultimapBuilder.hashKeys().hashSetValues().build();

    @Column(columnDefinition = "TEXT")
    @Convert(converter = MultiMap2JsonStringConverter.class)
    private SetMultimap<String, String> ruleActions = MultimapBuilder.hashKeys().hashSetValues().build();

    /**
     * 非规则提供的缓存，则可直接反序列化成Node
     * H开头为head node，还原时应该建立varTable
     */
    @Column(columnDefinition = "TEXT")
    @Convert(converter = MultiMapConverter.class)
    private Multimap<String, Multimap<String, String>> summaries = MultimapBuilder.hashKeys().hashSetValues().build();

    @Convert(converter = IntArray2JsonStringConverter.class)
    private int[][] pollutedPosition;

    // 分析过程中，可能会重复被使用的数据
    private transient Set<Call> callEdge = new HashSet<>(); // 需要重置
    private transient Set<Alias> childAliasEdges = new HashSet<>();
    private transient SootMethod sootMethod = null;
    private transient Body body = null;

    // 代表分析状态的字段，如果重新分析需要把这些都还原
    private boolean isBodyParseError = false; // 上次分析时拿不到body，这种情况不需要重复分析，也就是不用重置
    private boolean isInitialed = false; // 指代当前函数是否初始化完成
    private boolean isCallGraphInitialed = false; // 指代当前是否初始化过调用边，在存储边的时候应该设置为true
    private boolean isActionInitialed = false; // 指代当前actions是否被初始化过，在存储actions的时候应该设置为true
    private boolean isTimeout = false; // 上次分析是否超时
    private transient boolean isContainSomeError = false; // 上次分析时存在一些错误
    private transient int callCounter = 0; // 解决swap类型的冲突
    private transient AtomicInteger timeoutTimes = new AtomicInteger(0); // 全局的，超过3次认为当前函数分析会timeout，不需要再分析


    // 老算法解决冲突的字段
    private transient AtomicBoolean isRunning = new AtomicBoolean(false); // cached算法已经不用这个标志位了

    // 冲突解决相关字段
    private transient volatile Thread analysisOwnerThread;
    private transient final AtomicReference<AnalysisState> analysisState = new AtomicReference<>(AnalysisState.NOT_STARTED);
    private transient volatile CountDownLatch analysisCompletionLatch = new CountDownLatch(1);
    private transient final AtomicInteger waitingThreadsCount = new AtomicInteger(0);

    public static MethodReference newInstance(String name, String signature) {
        MethodReference methodRef = new MethodReference();
        String id = null;
        if (signature == null || signature.isEmpty()) {
            id = Hashing.md5()
                    .hashString(UUID.randomUUID().toString(), StandardCharsets.UTF_8)
                    .toString();
        } else {
            signature = signature.replace("'", ""); // soot生成的可能会带上'
            id = Hashing.md5() // 相同signature生成的id值也相同
                    .hashString(signature, StandardCharsets.UTF_8)
                    .toString();
        }
        methodRef.setName(name);
        methodRef.setId(id);
        methodRef.setSignature(signature);
        return methodRef;
    }

    public static MethodReference newInstance(String classname, SootMethod method) {
        MethodReference methodRef = newInstance(method.getName(), method.getSignature());
        methodRef.setClassname(classname);
        methodRef.setName0(String.format("%s#%s", classname, method.getName()));
        methodRef.setModifiers(method.getModifiers());
        methodRef.setPublic(method.isPublic());
        methodRef.setSubSignature(method.getSubSignature());
        methodRef.setStatic(method.isStatic());
        methodRef.setNative(method.isNative());
        methodRef.setPhantom(method.isPhantom());
        Set<String> types = new HashSet<>();
        types.add(method.getReturnType().toString());
        methodRef.setReturnType(types);
        methodRef.setReturnConstantType(SemanticUtils.isConstant(method.getReturnType()));
        methodRef.setAbstract(method.isAbstract());
        methodRef.setSootMethod(method);
        if (method.getParameterCount() > 0) {
            methodRef.setHasParameters(true);
            methodRef.setParameterSize(method.getParameterCount());
            // 移除method param的记录
//            for(int i=0; i<method.getParameterCount();i++){
//                List<Object> param = new ArrayList<>();
//                param.add(i); // param position
//                param.add(method.getParameterType(i).toString()); // param type
//                methodRef.getParameters().add(gson.toJson(param));
//            }
        }
        methodRef.setAnnotations(SemanticUtils.getAnnotations(method.getTags()));

        return methodRef;
    }

    public boolean isEverTimeout() {
        return timeoutTimes.get() >= 3;
    }

    public void incrementTimeoutTimes() {
        timeoutTimes.incrementAndGet();
    }

    public SootMethod getMethod() {
        if (sootMethod != null) return sootMethod;

        SootClass sc = SemanticUtils.getSootClass(classname);
        if (!sc.isPhantom()) {
            sootMethod = SemanticUtils.getMethod(sc, subSignature);
            return sootMethod;
        }

        return null;
    }

    public void setMethod(SootMethod method) {
        if (sootMethod == null) {
            sootMethod = method;
        }
    }

    public void setBody(Body body) {
        if (this.body == null) {
            this.body = body;
        }
    }

    public void setType(String type) {
        this.type = type;
        this.isEndpoint = "web".equals(type);
        this.isNettyEndpoint = "netty".equals(type);
    }

    public boolean isRpc() {
        return "rpc".equals(type);
    }

    public boolean isDao() {
        return "dao".equals(type);
    }

    public boolean isMRpc() {
        return "mrpc".equals(type);
    }

    public boolean isRunning() {
        return isRunning.get();
    }

    public void setRunning(boolean flag) {
        isRunning.set(flag);
    }

    public void addCallCounter() {
        callCounter += 1;
    }

    public void reset(){
        this.isInitialed = false;
        this.isCallGraphInitialed = false;
        this.isActionInitialed = false;
        this.callCounter = 0;
        this.callEdge = new HashSet<>();
        this.isRunning = new AtomicBoolean(false);
        this.analysisState.set(AnalysisState.NOT_STARTED);
        this.analysisCompletionLatch = new CountDownLatch(1);
        this.waitingThreadsCount.set(0);
    }

    public boolean isFinished(){
        if(isSuccessfullyFinished()) return true; // 已经初始化好了

        if(analysisState.get() == AnalysisState.FAILED ||
            analysisState.get() == AnalysisState.TIMEOUT
        ) return true;

        return false;
    }

    public boolean isSuccessfullyFinished(){
        if(isInitialed) return true; // 已经初始化好了
        if(isCallGraphInitialed && isActionInitialed){
            isInitialed = true;
            return true; // 已经初始化好了
        }

        if(analysisState.get() == AnalysisState.COMPLETED) return true;

        return false;
    }

    public boolean isContainSomeError(){
        if(isBodyParseError) return true;
        return isContainSomeError;
    }

    /**
     * Thread-safe method to add an action to the actions multimap
     * @param key the key to add
     * @param value the value to add
     */
    public synchronized void addAction(String key, String value) {
        actions.put(key, value);
    }

    public void addActions(Multimap<String, String> other) {
        actions.putAll(other);
    }

    /**
     * Thread-safe method to add multiple actions to the actions multimap
     * @param key the key to add
     * @param values the values to add
     */
    public synchronized void addActions(String key, Collection<String> values) {
        actions.putAll(key, values);
    }

    /**
     * Thread-safe method to add multiple actions to the actions multimap
     */
    public synchronized void removeActions(Set<String> keys) {
        keys.forEach(actions::removeAll);
    }

    /**
     * Thread-safe method to get all actions for a given key
     * @param key the key to look up
     * @return an unmodifiable set of values for the given key
     */
    public synchronized Set<String> getActions(String key) {
        return Collections.unmodifiableSet(actions.get(key));
    }

    /**
     * Thread-safe method to get all actions
     * @return an unmodifiable view of the actions multimap
     */
    public synchronized SetMultimap<String, String> getUnmodifiableActions() {
        return Multimaps.unmodifiableSetMultimap(actions);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MethodReference that = (MethodReference) o;
        return Objects.equal(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    // ==================== 冲突解决相关方法 ====================

    /**
     * 尝试开始分析
     * @return 是否成功获取分析权限
     */
    public boolean tryStartAnalysis() {
        if (analysisState.compareAndSet(AnalysisState.NOT_STARTED, AnalysisState.IN_PROGRESS)) {
            this.analysisOwnerThread = Thread.currentThread();
            this.setRunning(true);
            return true;
        }
        return false;
    }

    public void release(){
        // 通知等待的线程
        CountDownLatch latch = this.analysisCompletionLatch;
        if (latch != null) {
            latch.countDown();
        }

        // 重置latch为下次使用做准备
        this.analysisCompletionLatch = new CountDownLatch(1);
    }

    /**
     * 仅用于异常情况下，完成分析
     * @param success 是否成功
     */
    public void completeAnalysis(boolean success) {
        AnalysisState targetState = success ? AnalysisState.COMPLETED : AnalysisState.FAILED;
        AnalysisState currentState = analysisState.get();

        switch (currentState) {
            case NOT_STARTED: // 强制更新，后续也不需要分析了
                analysisState.set(targetState);
                this.setInitialed(true);
                break;
            case IN_PROGRESS: // 分析被中断了，转成NOT_STARTED，后续可尝试再分析一次
                analysisState.set(success ? AnalysisState.NOT_STARTED: AnalysisState.FAILED);
                this.setInitialed(!success);
                break;
            case TIMEOUT:
            case COMPLETED:
            case FAILED:
        }
        release();
    }

    /**
     * 等待分析完成
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否在超时前完成
     */
    public boolean waitForAnalysisCompletion(long timeoutMs) {
        CountDownLatch latch = this.analysisCompletionLatch;
        if (latch == null || latch.getCount() == 0) {
            return true; // 已经完成
        }

        try {
            return latch.await(timeoutMs, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("Interrupted while waiting for analysis completion: {}", this.getSignature());
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 增加等待线程计数
     * @return 当前等待线程数（包括刚增加的）
     */
    public int incrementWaitingThreads() {
        return waitingThreadsCount.incrementAndGet();
    }

    /**
     * 减少等待线程计数
     * @return 当前等待线程数（减少后的）
     */
    public int decrementWaitingThreads() {
        return waitingThreadsCount.decrementAndGet();
    }

    /**
     * 获取当前等待线程数
     * @return 等待线程数
     */
    public int getWaitingThreadsCount() {
        return waitingThreadsCount.get();
    }

    /**
     * 获取分析状态
     * @return 当前分析状态
     */
    public AnalysisState getAnalysisState() {
        return analysisState.get();
    }

    public void setAnalysisState(AnalysisState state) {
        analysisState.set(state);
    }

    /**
     * 获取分析拥有者线程
     * @return 拥有分析权限的线程
     */
    public Thread getAnalysisOwnerThread() {
        return analysisOwnerThread;
    }
}
