package tabby.common.bean.converter;


import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import com.google.gson.reflect.TypeToken;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import tabby.common.utils.JsonUtils;

import java.util.*;

/**
 * @project tabby_ng
 * @since 2025/4/16
 */
@Converter
public class MultiMapConverter implements AttributeConverter<Multimap<String, Multimap<String, String>>, String> {

    private static final TypeToken<Map<String, Collection<Map<String, Collection<String>>>>> mapTypeToken =
            new TypeToken<>() {};

    @Override
    public String convertToDatabaseColumn(Multimap<String, Multimap<String, String>> attribute) {
        if (attribute == null || attribute.isEmpty()) {
            return "{}";
        }
        Map<String, Collection<Map<String, Collection<String>>>> data = new HashMap<>();

        attribute.forEach((k, v) -> {
            if(data.containsKey(k)) {
                data.get(k).add(v.asMap());
            }else{
                Set<Map<String, Collection<String>>> set = new HashSet<>();
                set.add(v.asMap());
                data.put(k, set);
            }
        });

//        return JsonUtils.toJson(data, mapTypeToken.getType());
        return JsonUtils.toJsonWithReplace(data, mapTypeToken.getType());
    }

    @Override
    public Multimap<String, Multimap<String, String>> convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.isEmpty() || dbData.equals("{}")) {
            return MultimapBuilder.hashKeys().hashSetValues().build();
        }
        Map<String, Collection<Map<String, Collection<String>>>>
                data = JsonUtils.fromJsonWithReplace(dbData, mapTypeToken.getType());

        Multimap<String, Multimap<String, String>> attribute = MultimapBuilder.hashKeys().hashSetValues().build();

        data.forEach((k, v) -> {
            v.forEach(entry -> {
                Multimap<String, String> temp = MultimapBuilder.hashKeys().hashSetValues().build();
                entry.forEach(temp::putAll);
                attribute.put(k, temp);
            });
        });

        return attribute;
    }
}
