package tabby.common.bean.converter;

import com.google.common.collect.MultimapBuilder;
import com.google.common.collect.SetMultimap;
import com.google.gson.reflect.TypeToken;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import tabby.common.utils.JsonUtils;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/1/8
 */
@Converter
public class MultiMap2JsonStringConverter implements AttributeConverter<SetMultimap<String, String>, String> {

    @Override
    public String convertToDatabaseColumn(SetMultimap<String, String> attribute) {
        if (attribute == null || attribute.isEmpty()) {
            return "{}";
        }
//        return JsonUtils.toJson(attribute.asMap());
        return JsonUtils.toJsonWithReplace(attribute.asMap());
    }

    @Override
    public SetMultimap<String, String> convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.isEmpty() || "{}".equals(dbData)) {
            return MultimapBuilder.hashKeys().hashSetValues().build();
        }
        Type objectType = new TypeToken<Map<String, Set<String>>>() {}.getType();
        Map<String, Set<String>> temp = JsonUtils.fromJsonWithReplace(dbData, objectType);
        SetMultimap<String, String> result = MultimapBuilder.hashKeys().hashSetValues().build();
        temp.forEach(result::putAll);
        return result;
    }
}
