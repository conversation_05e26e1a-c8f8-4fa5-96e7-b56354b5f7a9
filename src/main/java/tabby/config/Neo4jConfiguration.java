package tabby.config;

import org.neo4j.driver.*;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.neo4j.core.DatabaseSelectionProvider;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jOperations;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.data.neo4j.core.mapping.Neo4jMappingContext;
import org.springframework.data.neo4j.core.transaction.Neo4jTransactionManager;

import static tabby.config.GlobalConfiguration.*;


/**
 * <AUTHOR>
 * @since 2022/9/5
 */
@Configuration
//@EnableTransactionManagement
//@EntityScan(basePackages="tabby.neo4j.entity")
//@EnableNeo4jRepositories("tabby.neo4j.repository")
public class Neo4jConfiguration {

    @Bean
    @ConditionalOnExpression("T(tabby.config.GlobalConfiguration).IS_LOAD")
    public Driver driver() {
        AuthToken authToken = AuthTokens.basic(NEO4J_USERNAME, NEO4J_PASSWORD);
        Config config = Config.builder().build();
        return GraphDatabase.driver(NEO4J_URL, authToken, config);
    }

    @Bean({"neo4jTemplate"})
    @ConditionalOnMissingBean({Neo4jOperations.class})
    public Neo4jTemplate neo4jTemplate(
            Neo4jClient neo4jClient,
            Neo4jMappingContext neo4jMappingContext,
            Driver driver, DatabaseSelectionProvider databaseNameProvider, ObjectProvider<TransactionManagerCustomizers> optionalCustomizers
    ) {
        Neo4jTransactionManager transactionManager = new Neo4jTransactionManager(driver, databaseNameProvider);
        optionalCustomizers.ifAvailable((customizer) -> {
            customizer.customize(transactionManager);
        });
        return new Neo4jTemplate(neo4jClient, neo4jMappingContext, transactionManager);
    }
}
