package tabby.analysis.data;


import lombok.Getter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import tabby.common.utils.PositionUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @project tabby_ng
 * @since 2025/4/19
 */
@Getter
public class Taint {

    private final int index;
    private final String from;

    Taint(int index, String from) {
        this.index = index;
        this.from = from;
    }

    public boolean isThis(){
        return index == PositionUtils.THIS;
    }

    public boolean isParam(){
        return this.index >= 0;
    }

    public boolean isSource(){
        return index == PositionUtils.SOURCE;
    }

    public static Taint of(int index, String from){
        return new Taint(index, from);
    }

    public static Taint of(String identity, String from){
        return new Taint(PositionUtils.getPosition(identity), from);
    }

    public static Taint of(String cached){
        String[] split = cached.split(":");
        if(split.length == 2){
            return of(PositionUtils.getPosition(split[0]), split[1]);
        }else{
            return of(PositionUtils.getPosition(split[0]), "");
        }
    }

    public static Set<Taint> parse(String cached){
        Set<Taint> taints = new HashSet<>();
        String[] split = cached.split(",");
        for(String s : split){
            taints.add(Taint.of(s));
        }
        return taints;
    }

    public boolean isContainTaint(){
        return index != PositionUtils.NOT_POLLUTED_POSITION
                && index != PositionUtils.NULL_TYPE;
    }

    public static boolean isNotContainTaint(Set<Taint> taints){
        for(Taint taint : taints){
            if(taint.isContainTaint()){
                return false;
            }
        }
        return true;
    }

    public boolean isContainTaint(int... taintIds){
        return index != PositionUtils.NOT_POLLUTED_POSITION
                && index != PositionUtils.NULL_TYPE && Arrays.stream(taintIds).noneMatch(taintId -> taintId == index);
    }

    public static boolean isContainsTaint(Set<Taint> taints, String except){
        for(Taint taint : taints){
            if(taint.isContainTaint()){
                return except == null || !taint.getFrom().equals(except);
            }
        }

        return false;
    }

    public static boolean isContainsTaintId(Set<Integer> taintIds, String except){
        Set<Integer> copied = new HashSet<>(taintIds);
        if(except != null){
            copied.remove(PositionUtils.getPosition(except));
        }
        copied.remove(PositionUtils.NOT_POLLUTED_POSITION);
        copied.remove(PositionUtils.NULL_TYPE);
        return !copied.isEmpty();
    }

    public static Set<Integer> cleanTaintIds(Set<Integer> taintIds, String except){
        Set<Integer> copied = new HashSet<>(taintIds);
        if(except != null){
            copied.remove(PositionUtils.getPosition(except));
        }
        copied.remove(PositionUtils.NOT_POLLUTED_POSITION);
        copied.remove(PositionUtils.NULL_TYPE);
        return copied;
    }

    @Override
    public String toString() {
        return PositionUtils.getPosition(index)+":"+from;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof Taint taint)) return false;

        return new EqualsBuilder().append(index, taint.index).append(from, taint.from).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(index).append(from).toHashCode();
    }
}
