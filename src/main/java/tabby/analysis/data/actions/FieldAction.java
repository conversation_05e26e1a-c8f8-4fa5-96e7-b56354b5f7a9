package tabby.analysis.data.actions;


import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Taint;
import tabby.analysis.data.nodes.FakeNode;
import tabby.analysis.data.nodes.Node;
import tabby.analysis.data.nodes.NodeRef;
import tabby.common.utils.PositionUtils;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @project tabby_ng
 * @since 2025/4/21
 */
public class FieldAction extends Action {
    public String baseName;
    public String fieldName;

    public FieldAction(String action, String[] callSiteVarNames) {
        this(action, callSiteVarNames, true);
    }

    public FieldAction(String action, String[] callSiteVarNames, boolean isNeedParseVarName) {
        super(action);
        String[] split = action.split("<f>");
        baseName = split[0];
        fieldName = split[1];
        if(isNeedParseVarName && !actionName.isEmpty()){
            int index = PositionUtils.getPosition(baseName)+1;
            if(index>=0 && index < callSiteVarNames.length){
                varName = callSiteVarNames[index];
            }
        }
    }

    @Override
    public Set<NodeRef> getRefs(String var, boolean isNeedComplete, ValueContainer container) {
        Set<NodeRef> refs = new HashSet<>();
        Set<Node> baseNodes = container.getNodesByVarNames(var, varName);
        for(Node node : baseNodes){
            if(node != null && node.getFields().containsKey(fieldName)){
                refs.addAll(node.getFields().get(fieldName));
            }
        }
        return refs;
    }

    @Override
    public void assign(Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer) {
        if(baseName != null){
            Set<Taint> taints = new HashSet<>();
            if(isOnlyChangeTaints){
                NodeRef fakeRef = assignRefs.iterator().next();
                Node fakeNode = container.getNode(fakeRef);
                if(fakeNode != null){
                    taints.addAll(fakeNode.getTaints());
                }
            }
            Set<Node> nodes = container.getNodesByVarName(baseName);
            for(Node node : nodes){
                if(node == null) continue;
                if(node instanceof FakeNode fn){
                    Set<Integer> taintIds = readOnlyContainer.getTaintIdsByNodeRefs(assignRefs);
                    fn.getTaints().addAll(taintIds.stream()
                            .map(id -> Taint.of(id, ""))
                            .collect(Collectors.toSet()));
                }else if(isOnlyChangeTaints){
                    node.getFields().forEach((key, ref) -> {
                        Node fieldNode = container.getNode(ref);
                        if(fieldNode != null){
                            fieldNode.getTaints().addAll(taints);
                        }
                    });
                }else{
                    Set<Node> fieldNodes = container.getNodesByNodeRefs(assignRefs);
                    node.replaceFieldNodes(fieldName, fieldNodes, container);
                }
            }
        }
    }

    @Override
    public Set<NodeRef> assign(Node returnNode, Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer) {
        // 暂时不应该出现 return<f>f 的情况，如果后面规则有需求可以扩展
        return new HashSet<>();
    }
}
