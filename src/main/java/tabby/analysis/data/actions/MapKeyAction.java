package tabby.analysis.data.actions;


import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Taint;
import tabby.analysis.data.nodes.*;
import tabby.common.utils.PositionUtils;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @project tabby_ng
 * @since 2025/4/21
 */
public class MapKeyAction extends Action {

    public MapKeyAction(String action, String[] callSiteVarNames){
        this(action, callSiteVarNames, true);
    }

    public MapKeyAction(String action, String[] callSiteVarNames, boolean isNeedParseVarName){
        super(action);
        actionName = actionName.replace("<k>", "");
        if(isNeedParseVarName && !actionName.isEmpty()){
            int index = PositionUtils.getPosition(actionName)+1;
            if(index >= 0 && index < callSiteVarNames.length){
                varName = callSiteVarNames[index];
            }
        }
    }

    @Override
    public Set<NodeRef> getRefs(String var, boolean isNeedComplete, ValueContainer container) {
        Set<NodeRef> refs = new HashSet<>();
        Set<Node> mapNodes = container.getNodesByVarNames(var, varName);
        if(isNeedComplete){
            for(Node node : mapNodes){
                if(node instanceof MapNode mn){
                    refs.addAll(mn.getKeys());
                }else if(node instanceof FakeNode){
                    refs.add(node.makeRef());
                }
            }
        }else{
            refs.addAll(mapNodes.stream().map(Node::makeRef).collect(Collectors.toSet()));
        }
        return refs;
    }

    @Override
    public Set<Node> getNodes(Set<Node> baseNodes, boolean isNeedComplete, ValueContainer container) {
        if(isSpecialAction){
            Set<Node> nodes = new HashSet<>();
            for(Node node : baseNodes){
                if(node instanceof MapNode mn){
                    nodes.addAll(container.getNodesByNodeRefs(mn.getKeys()));
                }else if(node instanceof FakeNode){
                    nodes.add(node);
                }
            }
            return nodes;
        }else{
            Set<Node> fields = super.getNodes(baseNodes, isNeedComplete, container);
            Set<Node> ret = new HashSet<>();
            if(isNeedComplete){
                for(Node field : fields){
                    if(field instanceof MapNode mn){
                        ret.addAll(container.getNodesByNodeRefs(mn.getKeys()));
                    }else if(field instanceof FakeNode){
                        ret.add(field);
                    }
                }
            }else{
                ret.addAll(fields);
            }
            return ret;
        }
    }

    @Override
    public void assign(Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer) {
        if(varName != null){
            Set<Taint> taints = new HashSet<>();
            if(isOnlyChangeTaints){
                NodeRef fakeRef = assignRefs.iterator().next();
                Node fakeNode = container.getNode(fakeRef);
                if(fakeNode != null){
                    taints.addAll(fakeNode.getTaints());
                }
            }
            Set<Node> nodes = container.getNodesByVarName(varName);
            for(Node node : nodes){
                if(node == null) continue;
                if(node instanceof MapNode mn){
                    if(isOnlyChangeTaints){
                        mn.getKeys().forEach(ref -> {
                            Node keyNode = container.getNode(ref);
                            if(keyNode != null){
                                keyNode.getTaints().addAll(taints);
                            }
                        });
                    }else{
                        mn.getKeys().addAll(assignRefs);
                    }
                }else if(node instanceof FakeNode fn){
                    Set<Integer> taintIds = readOnlyContainer.getTaintIdsByNodeRefs(assignRefs);
                    fn.getTaints().addAll(taintIds.stream()
                            .map(id -> Taint.of(id, ""))
                            .collect(Collectors.toSet()));
                }
            }
        }
    }

    @Override
    public Set<NodeRef> assign(Node returnNode, Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer) {
        Set<NodeRef> refs = new HashSet<>();
        if(isReturnAction){
            if(isOnlyChangeTaints){
                // 不应该出现 return<k><s> 的情况
            }else if(returnNode instanceof MapNode mn){
                mn.getKeys().addAll(assignRefs);
                container.save(returnNode);
                refs.add(returnNode.makeRef());
            }
        }
        return refs;
    }

    @Override
    public String toString() {
        return actionName+"<k>";
    }
}
