package tabby.analysis.data.actions;


import com.google.common.collect.Sets;
import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Taint;
import tabby.analysis.data.nodes.Node;
import tabby.analysis.data.nodes.NodeRef;
import tabby.common.utils.PositionUtils;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @project tabby_ng
 * @since 2025/4/21
 */
public class Action {

    public String origin;
    public String actionName;
    public String varName = null;
    public boolean isOnlyChangeTaints = false;
    public boolean isReturnAction = false;
    public boolean isSpecialAction = false; // varName和actionName都是空，只为了处理 <k><v><a> 这种混杂的情况

    public Action() {
    }

    public Action(String action) {
        origin = action;
        isOnlyChangeTaints = action.endsWith("<s>");
        isReturnAction = action.startsWith("return");
        actionName = action.replace("<s>", "");
    }

    public Action(String action, String[] callSiteVarNames){
        this(action, callSiteVarNames, true);
    }

    public Action(String action, String[] callSiteVarNames, boolean isNeedParseVarName){
        this(action);
        if(isNeedParseVarName){
            int index = PositionUtils.getPosition(actionName) + 1;
            if(index >= 0 && index < callSiteVarNames.length){
                varName = callSiteVarNames[index];
            }
        }
    }

    public Set<NodeRef> getRefs(String var, boolean isNeedComplete, ValueContainer container) {
        return container.getNodeRefsByVarNames(var, varName);
    }

    public Set<Node> getNodes(Set<Node> baseNodes, boolean isNeedComplete, ValueContainer container) {
        Set<Node> ret = new HashSet<>();
        for(Node node : baseNodes){
            if(node != null){
                if(node.isFake()){
                    ret.add(node);
                    continue;
                }
                Collection<NodeRef> refs = node.getFields().get(actionName);
                if(refs.isEmpty()) continue;
                for(NodeRef ref : refs){
                    Node fieldNode = container.getNode(ref);
                    if(fieldNode != null){
                        ret.add(fieldNode);
                    }
                }
            }
        }
        return ret;
    }

//    public void complete(Set<Node> baseNodes, )

    public Set<Node> getNodes(String var, boolean isNeedComplete, ValueContainer container) {
        Set<NodeRef> refs = getRefs(var, isNeedComplete, container);
        Set<Node> nodes = Sets.newConcurrentHashSet();
        refs.forEach(ref -> {
            Node node = container.getNode(ref);
            if(node != null){
                nodes.add(node);
            }
        });
        return nodes;
    }

    public void assign(Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer){
        if(varName != null){
            if(isOnlyChangeTaints){
                NodeRef fakeRef = assignRefs.iterator().next();
                Node fakeNode = container.getNode(fakeRef);
                if(fakeNode != null){
                    Set<Node> nodes = container.getNodesByVarName(varName);
                    for(Node node : nodes){
                        node.getTaints().addAll(fakeNode.getTaints());
                    }
                }
            }else{
                container.relateToVarTables(varName, assignRefs, true);
            }
        }
    }

    /**
     * 只处理return类型
     * @param returnNode
     * @param assignRefs
     * @param container
     * @param readOnlyContainer
     */
    public Set<NodeRef> assign(Node returnNode, Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer){
        Set<NodeRef> refs = new HashSet<>();
        if(isReturnAction){
            if(isOnlyChangeTaints){
                Set<Integer> positions = readOnlyContainer.getTaintIdsByNodeRefs(assignRefs);
                returnNode.setTaints(
                        positions.stream().
                                map(taintId -> Taint.of(taintId, ""))
                                .collect(Collectors.toSet()));
                container.save(returnNode);
                refs.add(returnNode.makeRef());
            }else{
                refs.addAll(assignRefs);
            }
        }
        return refs;
    }

    public static Action of(String action, String[] callSiteVarNames){
        if(action.contains("<f>")){
            return new FieldAction(action, callSiteVarNames);
        }else if(action.contains("<a>")){
            return new ArrayAction(action, callSiteVarNames);
        }else if(action.contains("<k>")){
            return new MapKeyAction(action, callSiteVarNames);
        }else if(action.contains("<v>")){
            return new MapValueAction(action, callSiteVarNames);
        } else{
            return new Action(action, callSiteVarNames);
        }
    }

    public static Action of(String action, String[] callSiteVarNames, boolean isNeedParseVarName){
        if(action.contains("<f>")){
            return new FieldAction(action, callSiteVarNames, isNeedParseVarName);
        }else if(action.contains("<a>")){
            return new ArrayAction(action, callSiteVarNames, isNeedParseVarName);
        }else if(action.contains("<k>")){
            return new MapKeyAction(action, callSiteVarNames, isNeedParseVarName);
        }else if(action.contains("<v>")){
            return new MapValueAction(action, callSiteVarNames, isNeedParseVarName);
        } else{
            return new Action(action, callSiteVarNames, isNeedParseVarName);
        }
    }

    public boolean isArrayOp(){
        return this instanceof ArrayAction;
    }

    public boolean isMapKeyOp(){
        return this instanceof MapKeyAction;
    }

    public boolean isMapValueOp(){
        return this instanceof MapValueAction;
    }

    public boolean isFieldOp(){
        return this instanceof FieldAction;
    }

    public boolean isNewOp(){
        return "newed".equals(actionName);
    }

    @Override
    public String toString() {
        return actionName;
    }
}
