package tabby.analysis.data.nodes;


import com.google.common.collect.Multimap;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import soot.Value;
import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Taint;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;

import java.util.HashSet;
import java.util.Set;

/**
 * @project tabby_ng
 * @since 2025/3/27
 */
@Getter
@Setter
public class MapNode extends Node {

    protected Set<NodeRef> keys = new HashSet<>(); // map类型的key放在keys字段，比如 map.put(k1,v1); k1->keys, v1->values
    protected Set<NodeRef> values = new HashSet<>();

    public MapNode() {
    }

    public MapNode(Value value) {
        super(value);
    }

    public static Node of(){
        return new MapNode();
    }

    public static Node of(Value value){
        Node n = new MapNode(value);
        n.uid = "M" + SemanticUtils.calculateUuid(value).substring(0, 8);
        return n;
    }

    @Override
    public String getIdentity() {
        return "M";
    }

    public void addKeyNode(Set<NodeRef> refs, ValueContainer container, boolean isNewCreated){
        Set<String> visited = new HashSet<>();
        for(NodeRef ref : refs){
            Node node = container.getNode(ref);
            if(node == null) continue;
            addKeyNode(node, container, visited, isNewCreated);
        }
    }

    public void addKeyNode(Node keyNode, ValueContainer container, Set<String> visited, boolean isNewCreated){
        if(keyNode == null || visited.contains(keyNode.getUid())) return;
        visited.add(keyNode.getUid());
        if(isNewCreated){
            Set<Taint> childTaints = genChildTaints(null, "k");
            keyNode.setTaints(childTaints);
        }

        boolean added = true;
        if(keyNode.isFake()){
            Set<NodeRef> refs = new HashSet<>(keys);
            for(NodeRef ref : refs){
                Node node = container.getNode(ref);
                if(node == null || !node.isFake()) continue;
                if(node.shallowEquals(keyNode)){
                    node.union(keyNode, visited, false, container, container);
                    added = false;
                }
            }
        }

        if(added && keys.size() <= GlobalConfiguration.ARRAY_MAX_LENGTH){
            keys.add(keyNode.makeRef());
        }
    }

    public void addValueNode(Set<NodeRef> refs, ValueContainer container, boolean isNewCreated){
        Set<String> visited = new HashSet<>();
        for(NodeRef ref : refs){
            Node node = container.getNode(ref);
            if(node == null) continue;
            addValueNode(node, container, visited, isNewCreated);
        }
    }

    public void addValueNode(Node valNode, ValueContainer container, Set<String> visited, boolean isNewCreated){
        if(valNode == null || visited.contains(valNode.getUid())) return;
        visited.add(valNode.getUid());
        if(isNewCreated){
            Set<Taint> childTaints = genChildTaints(null,"v");
            valNode.setTaints(childTaints);
        }

        boolean added = true;
        if(valNode.isFake()){
            Set<NodeRef> refs = new HashSet<>(values);
            for(NodeRef ref : refs){
                Node node = container.getNode(ref);
                if(node == null || !node.isFake()) continue;
                if(node.shallowEquals(valNode)){
                    node.union(valNode, visited, false, container, container);
                    added = false;
                }
            }
        }

        if(added && values.size() <= GlobalConfiguration.ARRAY_MAX_LENGTH){
            values.add(valNode.makeRef());
        }
    }

    public String toCacheString(){
        return String.format("%s[%s]", getIdentity(), type);
    }

    @Override
    public Set<String> genOthers() {
        Set<String> genKV = new HashSet<>();
        keys.forEach((nodeRef) -> {
            genKV.add(String.join("|", uid, "KEY", nodeRef.getType(), nodeRef.getStaticType(), nodeRef.getUid()));
        });

        values.forEach((nodeRef) -> {
            genKV.add(String.join("|", uid, "VALUE", nodeRef.getType(), nodeRef.getStaticType(), nodeRef.getUid()));
        });

        return genKV;
    }

    @Override
    public void recoverOthers(String record) {
        if(record.contains("|KEY|")){
            String[] split = record.split("\\|");
            String keyType = split[2];
            String isStatic = split[3];
            String keyUid = split[4];
            keys.add(new NodeRef(keyUid, keyType, isStatic));
        }else if(record.contains("|VALUE|")){
            String[] split = record.split("\\|");
            String valueType = split[2];
            String isStatic = split[3];
            String valueUid = split[4];
            values.add(new NodeRef(valueUid, valueType, isStatic));
        }
    }

    @Override
    public Multimap<String, String> genActions(String key, boolean isNeedNewOp, Set<String> visited, ValueContainer container) {
        Multimap<String, String> summary = super.genActions(key, isNeedNewOp, visited, container);

        if(!keys.isEmpty() && !key.endsWith("<s>")){
            keys.forEach((nodeRef) -> {
                if(visited.contains(nodeRef.getUid())) return ;
                visited.add(nodeRef.getUid());
                Node keyNode = container.getNode(nodeRef);
                if(keyNode != null){
                    summary.putAll(keyNode.genActions(key+"<k>", isNeedNewOp, visited, container));
                }
            });
        }

        if(!values.isEmpty() && !key.endsWith("<s>")){
            values.forEach((nodeRef) -> {
                if(visited.contains(nodeRef.getUid())) return ;
                visited.add(nodeRef.getUid());
                Node valueNode = container.getNode(nodeRef);
                if(valueNode != null){
                    summary.putAll(valueNode.genActions(key+"<v>", isNeedNewOp, visited, container));
                }
            });
        }

        return summary;
    }

    @Override
    protected void unionOther(Node other, Set<String> visited, ValueContainer container, ValueContainer otherContainer) {
        if(other instanceof MapNode mn && isMap()){
            Set<NodeRef> otherKeys = new HashSet<>(mn.getKeys());
            otherKeys.forEach((nodeRef) -> {
                if(keys.contains(nodeRef)){
                    Node keyNode = container.getNode(nodeRef);
                    Node otherKeyNode = otherContainer.getNode(nodeRef);
                    if(keyNode != null){
                        keyNode.union(otherKeyNode, visited, true, container, otherContainer);
                    }
                }else{
                    Node otherKeyNode = otherContainer.getNode(nodeRef);
                    addKeyNode(otherKeyNode, container, new HashSet<>(), false);
                    container.copyNodeFromOtherContainer(nodeRef, visited, otherContainer);
                }
            });

            Set<NodeRef> otherValues = new HashSet<>(mn.getValues());
            otherValues.forEach((nodeRef) -> {
                if(values.contains(nodeRef)){
                    Node valNode = container.getNode(nodeRef);
                    Node otherValNode = otherContainer.getNode(nodeRef);
                    if(valNode != null){
                        valNode.union(otherValNode, visited, true, container, otherContainer);
                    }
                }else{
                    Node otherValNode = otherContainer.getNode(nodeRef);
                    addValueNode(otherValNode, container, new HashSet<>(), false);
                    container.copyNodeFromOtherContainer(nodeRef, visited, otherContainer);
                }
            });
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof MapNode mapNode)) return false;

        return new EqualsBuilder()
                .appendSuper(super.equals(o))
                .append(keys, mapNode.keys)
                .append(values, mapNode.values).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .appendSuper(super.hashCode()).append(keys).append(values).toHashCode();
    }

    @Override
    public void clearOther() {
        super.clearOther();
        keys.clear();
        values.clear();
    }
}
