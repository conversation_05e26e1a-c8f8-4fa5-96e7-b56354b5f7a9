package tabby.analysis.data.nodes;


import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import soot.SootField;
import soot.Type;
import soot.Value;
import soot.jimple.AssignStmt;
import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Taint;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表示变量类型
 * 1. 普通类型 Val
 * 2. 常量类型 看模式，一般只记录String类型，常量数组类型不包含在常量
 * @project tabby_ng
 * @since 2025/3/27
 */
@Getter
@Setter
public class Node {

//    public String uid = "N" + UUID.randomUUID().toString().substring(0, 8);
    protected String uid = ""; // uid 如果是固定生成的话，会变成allocate-site
    // META 在创建时是固定的，后续不会发生变化
    protected String name = null; // 如果是field 可以填写下name; 如果是head，可以填写下变量名，不能为空
    protected Set<Taint> taints = new HashSet<>();
    // TODO taintType是否需要固定住可以再考虑下，如果simplify这种尝试直接改变污点的情况，是否set类型会更适合一点
    // 理论上，taintInfo和taintType是成对出现的，比如taintType为this，则taintInfo表示this或this的field。
    protected String type; // 当前变量类型
    protected String isStatic = "0";
    // FLD
    protected Multimap<String, NodeRef> fields = MultimapBuilder.hashKeys().hashSetValues().build(); // 只有 fields
//     PREV 暂时去除，后续有用的话再加进来
//    public NodeRef prev = null; // 用于查找父节点

    protected transient NodeRef ref = null; // 当前node的引用用于节省内存
    protected transient Type sootType;
    protected transient boolean isModified = false;

    public Node(){
    }

    public Node(Value value){
        sootType = SemanticUtils.getValueType(value);
        type = sootType.toString();
    }

    public static Node of(){
        return new Node();
    }

    public static Node of(Value value){
        Node n = new Node(value);
        n.uid = "N" + SemanticUtils.calculateUuid(value).substring(0, 8);
        return n;
    }

    public void resetUid(Object obj, boolean isNeedAddUid){
        String newed = isNeedAddUid?uid+obj.toString():obj.toString();
        uid = getIdentity() + SemanticUtils.calculateUuid(newed).substring(0, 8);
    }

    public boolean isConstant(){
        return this instanceof ConstantNode;
    }

    public boolean isMap(){
        return this instanceof MapNode;
    }

    public boolean isArray(){
        return this instanceof ArrayNode;
    }

    public boolean isNew(){
        return this instanceof NewNode;
    }

    public boolean isThisFieldObj(){
        for(Taint taint : taints){
            if(taint.isThis() && taint.getFrom().contains("<f>")){
                return true;
            }
        }
        return false;
    }

    public boolean isNeedProcessFields(){
        return !(isFake() || isConstant() || isNew());
    }

    public boolean isNull(){
        return this instanceof NullNode;
    }

    public boolean isFake(){
        return this instanceof FakeNode;
    }

    public boolean isTail(){
        return fields == null || fields.isEmpty();
    }

//    public boolean isHead(){
//        return prev == null;
//    }

    public boolean isStatic(){
        return isStatic.equals("1");
    }

    public void setType(Type type){
        sootType = type;
        this.type = type.toString();
    }

    public void setType(String type){
        this.type = type;
    }

    public Set<Taint> genChildTaints(String name, String tag){
        Set<Taint> childTaints = new HashSet<>();
        if(PositionUtils.isPositionTag(name)) return taints;

        for(Taint taint : taints){
            int index = taint.getIndex();
            String taintInfo = "";
            String from = taint.getFrom();
            if(from == null || from.isEmpty()) continue;
            if(name != null){
                if(from.endsWith("<s>")){
                    // <s> 不允许新增field
                    taintInfo = from;
                }else{
                    taintInfo = String.format("%s<f>%s", taint.getFrom(), name);
                }
                taintInfo = SemanticUtils.adjustAccessPath(taintInfo, name);
            }else if(tag != null){
                int inx = from.lastIndexOf("<f>");
                boolean isContainsTag = false;
                if(inx != -1){
                    String last = from.substring(inx+3);
                    isContainsTag = last.contains("<"+tag+">");
                }

                if(isContainsTag){
                    taintInfo = taint.getFrom();
                }else{
                    taintInfo = String.format("%s<%s>", taint.getFrom(), tag);
                }
            }
            childTaints.add(Taint.of(index, taintInfo));
        }
        return childTaints;
    }

    public void replaceFieldNodes(String fieldName, Set<Node> fieldNodes, ValueContainer container){
        if(fieldName == null || fieldName.isEmpty() || PositionUtils.isPositionTag(fieldName)) return;
        fields.removeAll(fieldName);
        for(Node node : fieldNodes){
            addFieldNode(fieldName, node, new HashSet<>(), container, false);
        }
    }

    public void addFieldNode(String fieldName, Node fieldNode, Set<String> visited, ValueContainer container, boolean isNewCreated){
        if(fieldNode == null || fieldNode.getUid().equals(uid) || visited.contains(fieldNode.getUid())) return;
        visited.add(fieldNode.getUid());
        if(fieldName == null || fieldName.isEmpty() || PositionUtils.isPositionTag(fieldName)) return;

        if(isNewCreated && !(fieldNode.isNull())){
            Set<Taint> fieldTaints = genChildTaints(fieldName, null);
            fieldNode.setTaints(fieldTaints);
        }

        if(fields.containsKey(fieldName)){
            Collection<NodeRef> fieldRefs = new ArrayList<>(fields.get(fieldName));
            boolean added = true;
            for(NodeRef ref : fieldRefs){
                Node node = container.getNode(ref);
                if(node == null) continue;
                if(node.shallowEquals(fieldNode)){
                    node.union(fieldNode, visited, false, container, container);
                    added = false;
                }
            }
            if(added && fieldRefs.size() <= GlobalConfiguration.ARRAY_MAX_LENGTH){
                fields.put(fieldName, fieldNode.makeRef());
            }
        }else{
            fields.put(fieldName, fieldNode.makeRef());
        }
    }

    public String toCacheString(){
        return String.format("%s[%s]%s", getIdentity(), type, name);
    }

    public Set<Integer> getTaintIds(){
        return taints.stream().map(Taint::getIndex).collect(Collectors.toSet());
    }

    public String getIdentity(){
        return "N";
    }

    /**
     * @param fieldName
     * @param n
     */
    public void replace(String fieldName, Node n){
//        Collection<NodeRef> nodes = fields.get(fieldName);
//        for(NodeRef nodeRef : nodes){
//            Node node = container.getNode(nodeRef);
//            if(node != null){
//                node.prev = null;
                // 原来的field变成了head
                // 如果有其他指针指向他，还是能用上，只是他不在属于原来head的field
//            }
//        }
        fields.replaceValues(fieldName, Collections.singleton(n.makeRef()));
    }

    public Set<String> serialize(){
        Set<String> serialized = new HashSet<>();
        // META
        serialized.add(genMeta());
//        // PREV
//        serialized.add(genPrev());
        // FLD
        serialized.addAll(genFields());
        // Others
        try{
            serialized.addAll(genOthers());
        }catch (Exception e){
            // ignore
        }
        return serialized;
    }

    public String genMeta(){
        if(taints.isEmpty()){
            taints.add(Taint.of(-3, ""));
        }
        return String.join("|",
                uid,
                "META",
                name,
                type,
                isStatic,
                String.join(",", taints.stream().map(Taint::toString).collect(Collectors.toSet())));
    }

    public Set<String> genFields(){
        Set<String> genFields = new HashSet<>();
        try{
            fields.forEach((fieldName, ref) -> {
                if(ref.getUid().startsWith("O")) return; // null 不cache
                genFields.add(
                        String.join("|",
                                uid, "FLD",
                                fieldName, ref.getType(), ref.getStaticType(), ref.getUid())
                );
            });
        }catch (Exception e){
            System.out.println(fields);
            e.printStackTrace();
        }

        return genFields;
    }

    public Set<String> genOthers(){
        // need to override
        throw new RuntimeException("Normal Node don't have other records.");
    }

    public void recoverMeta(String record){
        String[] split = record.split("\\|");
        uid = split[0];
        name = split[2];
        type = split[3];
        isStatic = split[4];
        taints = Taint.parse(split[5]);
    }

    public void recoverFields(String record){
        String[] split = record.split("\\|");
        String fieldType = split[3];
        String isStatic = split[4];
        String fieldUid = split[5];
        fields.put(split[2], new NodeRef(fieldUid, fieldType, isStatic));
    }

    public void recoverOthers(String record){
        // need to override
        throw new RuntimeException("Normal Node don't have other records.");
    }

    public static Node ofEmptyNode(String identity){
        return switch (identity) {
            case "A" -> ArrayNode.of();
            case "C" -> ConstantNode.of();
            case "M" -> MapNode.of();
            case "O" -> NullNode.of();
            case "F" -> FakeNode.of();
            default -> Node.of(); // N or others
        };
    }

    public static Node ofReturnNode(AssignStmt stmt, String returnType, Set<Integer> positions){
        Value lop = stmt.getLeftOp();
        String identity = SemanticUtils.judgeNodeType(lop);
        Node returnNode = Node.ofEmptyNode(identity);
        returnNode.uid = identity + SemanticUtils.calculateUuid(stmt).substring(0, 8);
        returnNode.type = returnType.toString();
        returnNode.name = "return";
        returnNode.taints.addAll(positions.stream().map(pos -> Taint.of(pos, "")).collect(Collectors.toSet()));
        return returnNode;
    }

    public static Node ofNodeWithValue(Value value){
        String identity = SemanticUtils.judgeNodeType(value);
        return switch (identity) {
            case "A" -> ArrayNode.of(value);
            case "C" -> ConstantNode.of(value);
            case "M" -> MapNode.of(value);
            case "O" -> NullNode.of();
            default -> Node.of(value); // N or others
        };
    }

    public static Node ofFieldNode(String base, SootField field){
        String identity = SemanticUtils.judgeNodeType(field);
        Node n = ofEmptyNode(identity);
        n.sootType = field.getType();
        n.type = field.getType().toString();
        n.name = "F:" +field.getName();
        n.uid = identity + SemanticUtils.calculateUuid(base + "<f>" + field).substring(0, 8);
        return n;
    }

    public static Node ofFieldNode(String base, String fieldName){
        String identity = "N"; //
        Node n = ofEmptyNode(identity);
        n.type = "java.lang.Object";
        n.name = "F:" + fieldName;
        n.uid = identity + SemanticUtils.calculateUuid(base + "<f>" + fieldName).substring(0, 8);
        return n;
    }

    public static Node deserialize(Collection<String> serialized){
        Node node = null;
        for(String record : serialized){
            if(node == null){
                String identity = record.substring(0, 1);
                if("H".equals(identity)){
                    identity = record.substring(1, 2);
                }
                node = ofEmptyNode(identity);
            }

            if(record.contains("|META|")){
                node.recoverMeta(record);
            }
            else if(record.contains("|FLD|")){
                node.recoverFields(record);
            }else{
                try{
                    node.recoverOthers(record);
                }catch (Exception e){
                    System.out.println("Error on recovering "+record);
                }
            }
        }

        if(node != null && node.uid.startsWith("H")){
            node.uid = node.uid.substring(1);
        }

        return node;
    }



    public Multimap<String, String> genActions(String key, boolean isNeedNewOp, Set<String> visited, ValueContainer container){
        Multimap<String, String> summary = MultimapBuilder.hashKeys().hashSetValues().build();

        if(key.endsWith("<s>")){
            // collect all taints
            Set<Integer> taintIds = container.getTaintIdsByNode(this);
            Set<Integer> cleaned = Taint.cleanTaintIds(taintIds, key);
            if(!cleaned.isEmpty()){
                summary.putAll(key, cleaned.stream().map(PositionUtils::getPosition).collect(Collectors.toSet()));
            }
            return summary;
        }

        if(!PositionUtils.isTaintFreeTag(key)){ // this param-n 本身不会在函数内部发生变化，只有他的field有变化才会被记录下来
            if(Taint.isContainsTaint(taints, key)){
                int keyIndex = PositionUtils.getPosition(key);
                for(Taint taint : taints){
                    if(taint.isContainTaint() && (taint.getFrom().contains("<f>") || taint.getIndex() != keyIndex)){
                        summary.put(key, taint.getFrom().isEmpty()?PositionUtils.getPosition(taint.getIndex()):taint.getFrom());
                    }
                }
                summary.remove(key, SemanticUtils.cleanEndTag(key));
            }else if(isNeedNewOp && Taint.isNotContainTaint(taints)){
                // 有可能在函数内部发生了替换的操作
                if(!key.endsWith("<s>") && !key.endsWith("<k>") && !key.endsWith("<v>") && !key.endsWith("<a>")){
                    summary.put(key, "newed"); // 在summary处理的地方也要处理这个
                }
            }
        }

        if(!fields.isEmpty() && !isFake()){
            fields.forEach((fieldName, nodeRef) -> {
                if(visited.contains(nodeRef.getUid())) return ;
                visited.add(nodeRef.getUid());
                Node fieldNode = container.getNode(nodeRef);
                if(fieldNode != null){
                    String fieldKey = null;
                    if(key.endsWith("<s>")){
                        fieldKey = key;
                    }else{
                        fieldKey = String.format("%s<f>%s", key, fieldName);
                        fieldKey = SemanticUtils.adjustAccessPath(fieldKey, fieldName);
                    }
                    summary.putAll(fieldNode.genActions(fieldKey, isNeedNewOp, visited, container));
                }
            });
        }

        return summary;
    }

    public String getValue(){
        throw new RuntimeException("Normal Node do not have value.");
    }

    public NodeRef makeRef(){
        if(ref != null){
            return ref;
        }

        return new NodeRef(uid, type, isStatic);
    }

    public Node clone(){
        Set<String> serialized = serialize();
        return deserialize(serialized);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof Node node)) return false;

        return new EqualsBuilder().append(uid, node.uid).append(name, node.name).append(taints, node.taints).append(type, node.type).append(isStatic, node.isStatic).append(fields, node.fields).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(uid).append(name).append(taints).append(type).append(isStatic).append(fields).toHashCode();
    }

    public boolean shallowEquals(Node other){
        if (this == other) return true;

        return new EqualsBuilder().append(type, other.type).append(isStatic, other.isStatic).isEquals();
    }

    /**
     * 返回true的情况下，other node 会被删除掉
     * @param other
     * @return
     */
    public void union(Node other, Set<String> visited, boolean isNeedSaveNode,
                      ValueContainer container, ValueContainer otherContainer){
        if(other == null || visited.contains(other.uid)) return ;

        // if(this.equals(other)) return ;

        visited.add(uid);
        visited.add(other.uid);

        if(this.shallowEquals(other)){
            // 合并taints
            taints.addAll(other.taints);
            // 合并fields
            unionFields(other, visited, container, otherContainer);
            // 合并其他字段
            unionOther(other, visited, container, otherContainer);
        }else if(isNeedSaveNode){
            container.save(other);
        }
        isModified = true;
    }

    protected void unionFields(Node other, Set<String> visited, ValueContainer container, ValueContainer otherContainer){
        Set<String> fieldNames = other.fields.keySet();
        for(String fieldName : fieldNames){
            Collection<NodeRef> fieldRefs = fields.get(fieldName);
            Collection<NodeRef> otherFieldRefs = other.fields.get(fieldName);
            if(fieldRefs.isEmpty() && otherFieldRefs.isEmpty()) continue;
            if(fieldRefs.isEmpty()){
                for(NodeRef ref:otherFieldRefs){
                    Node f = otherContainer.getNode(ref);
                    if(f != null){
                        addFieldNode(fieldName, f, new HashSet<>(), otherContainer, false);
                    }
                }
                for(NodeRef otherFieldRef : otherFieldRefs){
                    container.copyNodeFromOtherContainer(otherFieldRef, visited, otherContainer);
                }
            }else if(otherFieldRefs.isEmpty()){
                // do nothing
            }else{
                for(NodeRef otherFieldRef : otherFieldRefs){
                    if(fieldRefs.contains(otherFieldRef)){
                        Node node = container.getNode(otherFieldRef);
                        Node otherNode = otherContainer.getNode(otherFieldRef);
                        if(node != null && otherNode != null){
                            node.union(otherNode, visited, true, container, otherContainer);
                        }else if(otherNode != null){
                            container.copyNodeFromOtherContainer(otherNode.makeRef(), visited, otherContainer);
                        }
                    }else{
                        Node n = otherContainer.getNode(otherFieldRef);
                        addFieldNode(fieldName, n, new HashSet<>(), otherContainer, false);
                        container.copyNodeFromOtherContainer(otherFieldRef, visited, otherContainer);
                    }
                }

            }
        }
    }

    protected void unionOther(Node other, Set<String> visited,
                            ValueContainer container, ValueContainer otherContainer){
        // do nothing
    }

    public void clearOther(){
        fields.clear();
        taints.clear();
    }
}
