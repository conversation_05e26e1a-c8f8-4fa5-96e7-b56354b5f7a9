package tabby.analysis.data.nodes;


import lombok.Getter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import soot.Value;
import soot.jimple.StaticFieldRef;
import tabby.common.utils.SemanticUtils;

/**
 * 用于关联Node实体，一旦创建不可更改
 * @project tabby_ng
 * @since 2025/3/31
 */
@Getter
public class NodeRef implements Cloneable {
    private final String uid;
    private final String type;
    private final String staticType;

    public NodeRef(String uid, String type, String staticType) {
        this.uid = uid;
        this.type = type;
        this.staticType = staticType;
    }

    public static NodeRef of(Value value){
        String uid = SemanticUtils.makeNodeUid(value);
        String type = value.getType().toString();
        String isStatic = "0";
        if(value instanceof StaticFieldRef){
            isStatic = "1";
        }
        return new NodeRef(uid, type, isStatic);
    }

    public boolean isStatic() {
        return staticType.equals("1");
    }

    @Override
    public String toString() {
        return String.format("%s|%s|%s", uid, type, staticType);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof NodeRef nodeRef)) return false;

        return new EqualsBuilder().append(uid, nodeRef.uid).append(type, nodeRef.type).append(staticType, nodeRef.staticType).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(uid).append(type).append(staticType).toHashCode();
    }

    @Override
    public NodeRef clone() {
        return new NodeRef(uid, type, staticType);
    }
}
