package tabby.analysis;

import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import soot.Body;
import soot.SootMethod;
import soot.Unit;
import soot.jimple.Stmt;
import soot.toolkits.graph.BriefUnitGraph;
import soot.toolkits.graph.UnitGraph;
import soot.toolkits.scalar.ForwardFlowAnalysis;
import tabby.analysis.common.AnalysisManager;
import tabby.analysis.common.MethodContext;
import tabby.analysis.common.enums.AnalysisState;
import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Context;
import tabby.analysis.data.nodes.Node;
import tabby.analysis.data.nodes.NodeRef;
import tabby.analysis.model.CallEdgeBuilder;
import tabby.analysis.switcher.StmtSwitcher;
import tabby.common.bean.edge.Call;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;
import tabby.core.container.DataContainer;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/26
 */
@Setter
@Getter
@Slf4j
public class CachedPointerAnalysis extends ForwardFlowAnalysis<Unit, ValueContainer> implements AutoCloseable {

    private Context context;
    private Node retValue = null;
    private StmtSwitcher stmtSwitcher = new StmtSwitcher();
    private CallEdgeBuilder builder = null;
    private Set<String> initialedCallEdge = new HashSet<>();
    private Map<String, Integer> triggerTimes = new HashMap<>();
    private Set<Call> callEdges = new HashSet<>();
    private Multimap<String, Multimap<String, String>> actions =
            MultimapBuilder.hashKeys().hashSetValues().build();
    public static Map<String, MethodContext> runningMethods = Collections.synchronizedMap(new HashMap<>());

    /**
     * Construct the analysis from a DirectedGraph representation of a Body.
     *
     * @param graph
     */
    public CachedPointerAnalysis(UnitGraph graph) {
        super(graph);
    }

    @Override
    protected void flowThrough(ValueContainer in, Unit d, ValueContainer out) {
        // 多线程的情况下，先去判断当前method是否已经由其他线程分析完了，如果分析完了就不再继续分析
        if (context.isForceStop() || context.isAnalyseTimeout() ||
                GlobalConfiguration.GLOBAL_FORCE_STOP) {
            // 程序分析因为一些原因导致死循环 但仍然保存call边
            // 或 OOM 直接结束所有分析
            return;
        }

        if (context.getMethodReference().isFinished()) {
            // 多个线程同时分析一个函数，且当前线程落后其他线程，则直接跳过后续的分析，且不保存当前分析所得的调用边
            return;
        }

        if (context.isTimeout()) { // 如果当前函数分析超多最大限时，则停止分析当前函数
            return;
        }

//        String debugSig = "<java.util.regex.Pattern: java.util.regex.Pattern$Node expr(java.util.regex.Pattern$Node)>";
//        if(debugSig.equals(context.getMethodSignature())){
//            System.out.println(1);
//        }

        // try to analysis next stmt
        copy(in, out);
        context.accept(out);
        stmtSwitcher.accept(context);
        stmtSwitcher.accept((Stmt) d);
        stmtSwitcher.setTriggerTimes(triggerTimes);
        d.apply(stmtSwitcher); // TODO debug
        out.getCache().invalidateAll(); // 保存下所有需要保存的内容

//        if(debugSig.equals(context.getMethodSignature())){
//            System.out.println("\n");
//            diff(in, out);
//            System.out.println(context.getMethodSignature());
//            System.out.println(d);
//        }
    }

    @Override
    protected ValueContainer newInitialFlow() {
        return new ValueContainer(context.getDataContainer());
    }

    @Override
    protected void merge(ValueContainer in1, ValueContainer in2, ValueContainer out) {
        copy(in1, out);
        // out = in1 + in2
        out.union(in2);
    }

    @Override
    protected void copy(ValueContainer source, ValueContainer dest) {
        dest.copy(source);
    }

    public void doAnalysis() {
        super.doAnalysis();
    }

    /**
     * 更新ref状态，保存必要数据
     */
    public void doEnd() {
        AnalysisState state = AnalysisState.COMPLETED;
        MethodReference ref = context.getMethodReference();
        boolean isNormalExit = !context.isForceStop() && !context.isAnalyseTimeout();
        context.setNormalExit(isNormalExit); // 如果分析过程中出现了死循环，下次不再分析，用simplify处理

        if (isNormalExit) {
            if(!ref.isCallGraphInitialed()){ // 保存调用边关系
                context.getDataContainer().store(stmtSwitcher.getCallEdges(), false);
                ref.setCallGraphInitialed(true);
            }

            if(!ref.isActionInitialed()){ // 保存actions
                ref.setSummaries(context.getSummaries());
                generateActions(ref);
                ref.setActionInitialed(true);
            }
            ref.setInitialed(true);
        }else if(context.isForceStop()){
            state = AnalysisState.FAILED; // 当前仅表示当前函数存在不动点无法到达的问题，算法问题不需要 try again
            log.warn("ForceStop has been called, skip this method {}.", ref.getSignature());
        }else if (context.isAnalyseTimeout()) { // 超时问题，需要 try again
            if (context.isTopContext()) {
                context.getDataContainer().getAnalyseTimeoutMethodSigs().add(context.getMethodSignature());
            } else {
                Context preContext = context.getPreContext();
                preContext.setAnalyseTimeout(true);
            }
            state = AnalysisState.TIMEOUT;

            if(ref.isEverTimeout()){ // 全局有3次超时了，不再分析
                if(!ref.isCallGraphInitialed()){ // 保存调用边关系
                    context.getDataContainer().store(stmtSwitcher.getCallEdges(), false);
                    ref.setCallGraphInitialed(true);
                }
            }
        }

        ref.setAnalysisState(state);
        ref.release();
    }

    public void generateActions(MethodReference ref) {
        if(ref.getRuleActions().isEmpty() && !ref.getSummaries().isEmpty()){
            ValueContainer valueContainer = new ValueContainer(ref.getSummaries(), context.getDataContainer());
            int size = context.getCopiedCurObjects().length;
            for(int i = 0; i < size; i++){
                if(i == 0){
                    Set<Node> nodes = valueContainer.getNodesByVarName("this");
                    for(Node n : nodes){
                        Multimap<String, String> temp = n.genActions("this", true, new HashSet<>(), valueContainer);
                        SemanticUtils.pureActions(temp);
                        ref.addActions(temp);
                    }
                }else{
                    Set<Node> nodes = valueContainer.getNodesByVarName("param-"+(i-1));
                    for(Node n : nodes){
                        Multimap<String, String> temp = n.genActions("param-"+(i-1), true, new HashSet<>(), valueContainer);
                        SemanticUtils.pureActions(temp);
                        ref.addActions(temp);
                    }
                }
            }
            Set<Node> nodes = valueContainer.getNodesByVarName("return");
            for(Node n : nodes){
                Multimap<String, String> temp = n.genActions("return", true, new HashSet<>(), valueContainer);
                SemanticUtils.pureActions(temp);
                ref.addActions(temp);
            }
        }
    }

    public static void analyse(String uuid, SootMethod method, MethodReference methodRef, DataContainer dataContainer) {
        try (Context context = Context.newInstance(methodRef.getSignature(), methodRef, dataContainer)) {
            runningMethods.put(uuid, context);
            CachedPointerAnalysis.processMethod(method, context);
        }
    }

    /**
     * 返回return value
     *
     * @param method
     * @param context
     * @return
     */
    public static boolean processMethod(SootMethod method, Context context) {
        // 使用新的分析管理器来处理冲突和递归检测
        return AnalysisManager.analyzeMethodWithAutoManagement(method, context);
    }

    /**
     * 内部方法：执行实际的方法分析逻辑
     * 这个方法被AnalysisManager调用，不应该直接使用
     *
     * @param body
     * @param context
     * @return
     */
    public static boolean processMethodInternal(Body body, Context context) {
        if (body != null) {
            UnitGraph graph = new BriefUnitGraph(body);
            MethodReference methodReference = context.getMethodReference();
            // initial
            methodReference.setCallCounter(0);
            // do analysis
            try (CachedPointerAnalysis analysis = new CachedPointerAnalysis(graph)) {
                analysis.setContext(context);
                analysis.setBuilder(new CallEdgeBuilder());
                analysis.doAnalysis();
                analysis.doEnd();
                return true;
            } // 可能会throw exception
        }
        return false;
    }


    /**
     * 测试debug用
     *
     * @param o1
     * @param o2
     */
    public static void diff(ValueContainer o1, ValueContainer o2) {
        System.out.println("var table");
        debugPrintNodeRefs(o1.getLocalVarTable(), o2.getLocalVarTable());
//        debugPrintNodeRefs(o1.getGlobalVarTable(), o2.getGlobalVarTable());
        System.out.println("nodes");
        o1.getCache().invalidateAll();
        o2.getCache().invalidateAll();
        debugPrint(o1.getNodeTable(), o2.getNodeTable());
    }

    public static void debugPrintNodeRefs(Multimap<String, NodeRef> o1, Multimap<String, NodeRef> o2) {
        Set<String> visited = new HashSet<>();
        Set<String> keys = o1.keySet();
        for (String key : keys) {
            if(visited.contains(key)) return;
            visited.add(key);
            Collection<NodeRef> o1Refs = o1.get(key);
            Collection<NodeRef> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }
        keys = o2.keySet();
        for (String key : keys) {
            if(visited.contains(key)) continue;
            visited.add(key);
            Collection<NodeRef> o1Refs = o1.get(key);
            Collection<NodeRef> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }
        visited.clear();
    }

    public static void debugPrint(Multimap<String, String> o1, Multimap<String, String> o2) {
        Set<String> visited = new HashSet<>();
        Set<String> keys = o1.keySet();

        for (String key : keys) {
            if(visited.contains(key)) continue;
            visited.add(key);
            Collection<String> o1Refs = o1.get(key);
            Collection<String> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }
        keys = o2.keySet();
        for (String key : keys) {
            if(visited.contains(key)) continue;
            visited.add(key);
            Collection<String> o1Refs = o1.get(key);
            Collection<String> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }

        visited.clear();
    }

    @Override
    public void close() {
        context = null;
        retValue = null;
        stmtSwitcher = null;
        triggerTimes.clear();
    }

    public static void stopAll(){
        for(MethodContext methodContext : runningMethods.values()){
            methodContext.setAnalyseTimeout(true);
        }
    }
}
