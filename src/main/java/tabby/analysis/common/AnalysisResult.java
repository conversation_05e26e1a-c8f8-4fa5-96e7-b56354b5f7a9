package tabby.analysis.common;


import lombok.Getter;
import tabby.analysis.common.enums.AnalysisResultType;

/**
 * 分析结果
 * @project tabby_ng
 * @since 2025/9/3
 */
@Getter
public class AnalysisResult {
    private final AnalysisResultType type;
    private final String reason;

    public AnalysisResult(AnalysisResultType type, String reason) {
        this.type = type;
        this.reason = reason;
    }

    public AnalysisResultType getType() {
        return type;
    }

    public String getReason() {
        return reason;
    }

    public boolean shouldProceed() {
        return type == AnalysisResultType.CAN_PROCEED ||
                type == AnalysisResultType.FORCE_PROCEED;
    }

    public boolean shouldSkip() {
        return type == AnalysisResultType.ALREADY_COMPLETED ||
                type == AnalysisResultType.COMPLETED_BY_OTHER;
    }
}
