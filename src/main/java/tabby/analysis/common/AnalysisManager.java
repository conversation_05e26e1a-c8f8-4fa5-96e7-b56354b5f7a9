package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;
import soot.Body;
import soot.SootMethod;
import tabby.analysis.common.enums.AnalysisResultType;
import tabby.analysis.common.enums.AnalysisState;
import tabby.analysis.CachedPointerAnalysis;
import tabby.analysis.data.Context;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;

/**
 * 统一的分析管理器
 * 集成冲突解决功能，递归检测由Context处理
 *
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class AnalysisManager {

    /**
     * 使用自动资源管理的方法分析
     * 简化版本，递归检测由Context处理
     * 返回false，则进行simple的逻辑
     * @param method Soot方法
     * @param context 分析上下文
     * @return 是否成功分析
     */
    public static boolean analyzeMethodWithAutoManagement(SootMethod method, Context context) {
        MethodReference ref = context.getMethodReference();
        // 检查基本条件
        if(ref.isSuccessfullyFinished()){
            return true;
        }else if (ref.isEverTimeout() || method == null || ref.isContainSomeError()) {
            return false;
        }else if(!GlobalConfiguration.IS_NEED_ANALYSIS_EVERYTHING && method.isStatic() && method.getParameterCount() == 0){
            return false;
        }else if(method.isPhantom() || method.isAbstract() || method.isNative()){
            return false;
        }else if(ref.isDao() || ref.isRpc() || ref.isMRpc()){
            SemanticUtils.applySpecialMethodSummary(ref, null);
            return true;
        }
        // 检查分析冲突并执行分析
        AnalysisResult conflictResult = tryStartAnalysis(ref);
        return switch (conflictResult.getType()) {
            case ALREADY_COMPLETED, COMPLETED_BY_OTHER -> true;
            case CAN_PROCEED, FORCE_PROCEED -> performActualAnalysis(method, context);
            default -> // timeout
                    false;
        };
    }

    /**
     * 执行实际的方法分析
     */
    private static boolean performActualAnalysis(SootMethod method, Context context) {
        MethodReference methodReference = context.getMethodReference();
        String methodSignature = methodReference.getSignature();
        
        try {
            // 获取方法体
            Body body = SemanticUtils.retrieveBody(method, methodSignature, true);

            // 检查方法体大小
            if (isMethodTooLarge(body, methodSignature)) {
                methodReference.completeAnalysis(false);
                return false;
            }

            // 执行分析
            return CachedPointerAnalysis.processMethodInternal(body, context);
        } catch (Exception e) {
            log.error("Error analyzing method {}: {}", methodSignature, e.getMessage(), e);
            
            // 处理异常情况
            handleAnalysisException(methodReference, methodSignature, e);
            methodReference.completeAnalysis(false);
            return false;
        }
    }

    /**
     * 尝试开始分析指定方法
     *
     * @param methodReference 方法引用
     * @return AnalysisResult 分析结果
     */
    public static AnalysisResult tryStartAnalysis(MethodReference methodReference) {
        // 快速检查：如果方法已经分析完成，直接返回
        if (methodReference.isFinished()) {
            return new AnalysisResult(AnalysisResultType.ALREADY_COMPLETED, "Already completed");
        }

        // 检查并发等待线程数
        int currentWaiters = methodReference.getWaitingThreadsCount();

        // 如果等待线程过多，强制开始新的分析
        if (currentWaiters > GlobalConfiguration.MAX_CONCURRENT_WAITERS) {
            log.warn("Too many waiting threads ({}), force proceeding for method: {}",
                    currentWaiters, methodReference.getSignature());
            return new AnalysisResult(AnalysisResultType.FORCE_PROCEED,
                    "Too many waiters: " + currentWaiters);
        }

        // 尝试获取分析权限
        if (methodReference.tryStartAnalysis()) {
            // 成功获取分析权限，可以开始分析
            return new AnalysisResult(AnalysisResultType.CAN_PROCEED, "Analysis started");
        }

        // 其他线程正在分析，等待完成
        return waitForAnalysisCompletion(methodReference, false);
    }

    /**
     * 等待其他线程完成分析
     */
    private static AnalysisResult waitForAnalysisCompletion(MethodReference methodReference, boolean isSecondRound) {
        long waitTime = isSecondRound? 3 : GlobalConfiguration.THREAD_TIMEOUT * 60L;

        methodReference.incrementWaitingThreads();
        boolean completed = methodReference.waitForAnalysisCompletion(waitTime);
        methodReference.decrementWaitingThreads();

        if (!completed && !isSecondRound) {
            log.warn("Timeout waiting for analysis completion: {}", methodReference.getSignature());
            return new AnalysisResult(AnalysisResultType.TIMEOUT, "Wait timeout");
        }

        // 检查分析结果
        AnalysisState finalState = methodReference.getAnalysisState();
        if (finalState == AnalysisState.COMPLETED
                || finalState == AnalysisState.FAILED
                || finalState == AnalysisState.TIMEOUT) {
            // 其他线程已完成分析，可能是completed、failed、timeout，当前线程直接返回
            return new AnalysisResult(AnalysisResultType.COMPLETED_BY_OTHER, "Completed by other thread");
        } else if(finalState == AnalysisState.IN_PROGRESS && !isSecondRound){
            // 可能被其他线程抢占了
            return waitForAnalysisCompletion(methodReference, true);
        } else {
            // 剩余状态not_started，当前线程可以重试
            return new AnalysisResult(AnalysisResultType.CAN_PROCEED, "Other thread failed");
        }
    }

    /**
     * 检查方法是否过大
     */
    private static boolean isMethodTooLarge(Body body, String methodSignature) {
        try {
            if(body != null){
                int bodySize = body.getUnits().size();
                if (bodySize >= GlobalConfiguration.METHOD_MAX_BODY_COUNT) {
                    log.debug("Method {} body is too big ({}), ignoring", methodSignature, bodySize);
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("Cannot check method body size for {}: {}", methodSignature, e.getMessage());
        }
        return false;
    }
    
    /**
     * 处理分析异常
     */
    private static void handleAnalysisException(MethodReference methodReference, 
                                              String methodSignature, Exception e) {
        String msg = e.getMessage();
        if (msg != null && msg.contains("Body retrieve error: ")) {
            methodReference.setBodyParseError(true);
            log.warn("Body retrieve error for method {}: {}", methodSignature, msg);
        } else {
            log.error("Unexpected error analyzing method {}: {}", methodSignature, msg, e);
            methodReference.setContainSomeError(true);
        }
    }




}
