package tabby.analysis.model;

import com.google.common.collect.Sets;
import soot.Type;
import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.PositionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/8/31
 */
public class ProxyInvokeModel extends Model {

    private static String PROXY_INVOKE = "java.lang.Object invoke(java.lang.Object,java.lang.reflect.Method,java.lang.Object[])";


    @Override
    boolean apply(InvokeExpr invokeExpr, MethodReference callee) {
        if ("java.lang.reflect.Proxy".equals(callee.getClassname())
                && "newProxyInstance".equals(callee.getName())) {
            Type sootType = getArgType(invokeExpr, 2);
            Set<String> argTypes = cgBuilder.types.get(3);
            Set<MethodReference> refs = getReferences(argTypes, sootType, PROXY_INVOKE);

            if(!refs.isEmpty()){
                List<Set<String>> types = new LinkedList<>();
                types.add(argTypes);
                types.add(Sets.newHashSet("java.lang.Object"));
                types.add(Sets.newHashSet("java.lang.reflect.Method"));
                types.add(Sets.newHashSet("java.lang.Object"));

                List<Set<Integer>> pos = new ArrayList<>();
                pos.add(cgBuilder.getPositions().get(3));
                pos.add(cgBuilder.getPositions().get(3));
                pos.add(Sets.newHashSet(PositionUtils.NOT_POLLUTED_POSITION));
                pos.add(cgBuilder.getPositions().get(3));

                cgBuilder.setTypes(types);
                cgBuilder.setPositions(pos);
                cgBuilder.setInvokeType("ManualInvoke");
                cgBuilder.setCallerThisFieldObj(false);

                needToReplace.addAll(refs);
                return true;
            }
        }
        return false;
    }
}
