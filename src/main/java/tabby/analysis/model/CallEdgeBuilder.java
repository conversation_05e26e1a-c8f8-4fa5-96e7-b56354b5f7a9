package tabby.analysis.model;

import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import soot.SootClass;
import soot.SootMethodRef;
import soot.Value;
import soot.jimple.*;
import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Context;
import tabby.analysis.data.nodes.NodeRef;
import tabby.common.bean.edge.Call;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;
import tabby.core.container.DataContainer;

import java.util.*;

/**
 * call边builder
 * 需要在分析当前invoke之前，把当前的所有信息保存起来
 * 所以在stmtSwitcher上用
 *
 * <AUTHOR>
 * @since 2022/2/17
 */
@Getter
@Setter
@Slf4j
public class CallEdgeBuilder {

    private final LinkedList<Model> chains = new LinkedList<>();
    private Context context;
    private Stmt stmt = null;
    private Set<MethodReference> needToAnalyze = new HashSet<>();
    private Set<MethodReference> needToBuildCallEdge = new HashSet<>(); // 用于记录当前调用点需要建边的函数
    private Set<MethodReference> candidateToAnalyze = new HashSet<>(); // 用于记录后续需要分析的函数
    private Set<MethodReference> candidateToBuildCallEdge = new HashSet<>();
    public List<Set<Integer>> positions = new LinkedList<>();
    public List<Set<String>> types = new LinkedList<>();
    public Map<String, String> methodToCallPositionMap = new HashMap<>(); // TODO 现在这个版本没办法同时保留被人工建立函数剔除的原有调用，有时间实现下method和call position的绑定
    public boolean isCallerThisFieldObj = false;
    public String invokeType;
    public int lineNumber;

    public CallEdgeBuilder() {
        chains.add(new IgnoreInvokeModel());
        chains.add(new AccessControllerInvokeModel());
        chains.add(new ThreadPoolRunnableInvokeModel());
        chains.add(new ThreadRunnableInvokeModel());
        chains.add(new ProxyInvokeModel());
        chains.add(new OptionalModel());
        chains.add(new XMLRPCSinkModel());
        chains.add(new Hibernate3TplExecuteModel());
        chains.add(new DefaultInvokeModel());
    }

    public void accept(Context context){
        this.context = context;
    }

    public void accept(Stmt stmt){
        this.stmt = stmt;
    }

    public void build(InvokeExpr ie, Set<Call> callEdges) {
        try {
            // pre process
            fresh();
            // do do do
            doDispatch(ie);
            doExtractInfos(ie);
            // 防止递归成环
            MethodReference caller = context.getMethodReference();
            needToAnalyze.remove(caller);
            needToBuildCallEdge.remove(caller);
            // 使用model来调整上面两个数组
            // model的作用，把最终的callee放到candidate，不需要的过滤掉
            // 暂时没有将xxxToAnalyze过滤掉
            for (Model model : chains) {
                model.accept(this);
                if (model.apply(ie)) {
                    break;
                }
            }
            // build call edge
            doCallEdgeBuild(callEdges);
            // doFinal
            doFinal();
        } catch (Exception e) {
            // 多线程的情况下 可能在soot提取method上会有一些问题
//            log.error(e.getMessage());
            e.printStackTrace();
        }
    }

    public void doFinal(){
        if(!needToAnalyze.isEmpty()){
            candidateToAnalyze.addAll(needToAnalyze);
        }
    }

    public void fresh(){
        needToAnalyze.clear();
        needToBuildCallEdge.clear();
        candidateToAnalyze.clear();
        candidateToBuildCallEdge.clear();
        types = new LinkedList<>();
        positions = new LinkedList<>();
    }

    public void doCallEdgeBuild(Set<Call> callEdges) {
        MethodReference caller = context.getMethodReference();

        for(MethodReference callee:candidateToBuildCallEdge){
            Call call =
                    Call.newInstance(caller, callee,
                            invokeType, isCallerThisFieldObj, positions, types);
            call.setLineNum(lineNumber);
            call.generateId();
            callEdges.add(call);
        }
    }

    public void doExtractInfos(InvokeExpr ie) {
        invokeType = SemanticUtils.getInvokeType(ie);
        lineNumber = stmt.getJavaSourceStartLineNumber();

        ValueContainer container = context.getContainer();
        // 边污点信息抽取
        if (ie instanceof InstanceInvokeExpr) {
            Value base = ((InstanceInvokeExpr) ie).getBase();
            types.add(container.getVarNodeTypes(base));
            Set<Integer> ids = container.getTaintIdsByValue(base);
            if(ids.isEmpty()){
                ids.add(PositionUtils.NOT_POLLUTED_POSITION);
            }else if(ids.size() >= 2){
                ids.remove(PositionUtils.NOT_POLLUTED_POSITION);
            }
            positions.add(ids);
            isCallerThisFieldObj = container.isThisObjField(base);
        } else {
            positions.add(Collections.singleton(PositionUtils.NOT_POLLUTED_POSITION));
            if(ie instanceof StaticInvokeExpr sie){
                types.add(Sets.newHashSet(sie.getMethodRef().getDeclaringClass().toString()));
            }else if(ie instanceof DynamicInvokeExpr die){
                SootMethodRef bootstrap = die.getBootstrapMethodRef();
                types.add(Sets.newHashSet(bootstrap.getDeclaringClass().toString()));
            } else {
                types.add(new HashSet<>());
            }
        }
        // get args obj
        for (Value arg : ie.getArgs()) {
            types.add(container.getVarNodeTypes(arg));
            Set<Integer> ids = container.getTaintIdsByValue(arg);
            if(ids.isEmpty()){
                ids.add(PositionUtils.NOT_POLLUTED_POSITION);
            }else if(ids.size() >= 2){
                ids.remove(PositionUtils.NOT_POLLUTED_POSITION);
            }
            positions.add(ids);
        }
    }

    public void doDispatch(InvokeExpr ie) {
        if (ie instanceof StaticInvokeExpr || ie instanceof SpecialInvokeExpr) {
            // 处理调用者类型固定的情况，直接取当前的method
            // 相对来说比较简单
            // 包括 静态函数调用，构造函数调用，实例的私有方法调用，实例的父类方法调用
            MethodReference targetMethodRef = context.getDataContainer().getOrAddMethodRef(ie);
            needToAnalyze.add(targetMethodRef);
            needToBuildCallEdge.add(targetMethodRef);
        } else if (ie instanceof InstanceInvokeExpr) {
            doComplexDispatch((InstanceInvokeExpr) ie);
        }
    }

    public void doComplexDispatch(InstanceInvokeExpr ie) {
        DataContainer dataContainer = context.getDataContainer();
        ValueContainer container = context.getContainer();
        MethodReference targetMethodRef = dataContainer.getOrAddMethodRef(ie);

        if(targetMethodRef == null) return ; // 拿不到不分析

        if (targetMethodRef.isActionInitialed() || targetMethodRef.isPhantom() || targetMethodRef.isNative()) {
            // 对于接口类型且规则提供的actions，则直接加入targets，其他不能被分析的也会被直接加入
            needToAnalyze.add(targetMethodRef);
            needToBuildCallEdge.add(targetMethodRef);
        }else if (GlobalConfiguration.IS_ENABLE_RTA_CALL_GRAPH_CONSTRUCT){ // RTA
            Value value = ie.getBase();
            tryToDispatchFromType(value, targetMethodRef, true);
        }else{ // 默认CHA ALIAS_METHOD_MAX_COUNT默认5，太大容易爆炸
            // TODO 这部分逻辑如果精度不高，则剔除，直接调用simplify来处理
            SootClass cls = SemanticUtils.getSootClass(targetMethodRef.getClassname());
            Set<MethodReference> aliasRefs = dataContainer.getAliasMethodRefs(cls, targetMethodRef.getSubSignature());
            if(aliasRefs.size() <= GlobalConfiguration.ALIAS_METHOD_MAX_COUNT){
                needToAnalyze.addAll(aliasRefs);
            }else{
                List<MethodReference> copied = new ArrayList<>(aliasRefs);
                needToAnalyze.addAll(copied.subList(0, GlobalConfiguration.ALIAS_METHOD_MAX_COUNT));
            }
            // 分析用具体的alias，建边还是用之前的interface类型
            needToBuildCallEdge.add(targetMethodRef);
            if(needToAnalyze.isEmpty()){
                if(targetMethodRef.isAbstract()){ // 可能存在alias未构建成功的情况，特别是web情况
                    Value value = ie.getBase();
                    tryToDispatchFromType(value, targetMethodRef, false);
                }
                if(needToAnalyze.isEmpty()){
                    needToAnalyze.add(targetMethodRef);
                }
            }
        }
    }

    public void tryToDispatchFromType(Value value, MethodReference targetMethodRef, boolean isNeedSaveToCallEdge) {
        DataContainer dataContainer = context.getDataContainer();
        ValueContainer container = context.getContainer();
        Collection<NodeRef> refs = container.getVarNodeRefs(value);
        for (NodeRef ref : refs) {
            String classname = ref.getType();
            if(classname == null) continue;
            MethodReference methodRef = dataContainer.getOrAddMethodRefBySubSignature(classname, targetMethodRef.getSubSignature());
            if(methodRef == null) continue;
            needToAnalyze.add(methodRef);
            if(isNeedSaveToCallEdge){
                needToBuildCallEdge.add(methodRef);
            }
        }
    }
}
