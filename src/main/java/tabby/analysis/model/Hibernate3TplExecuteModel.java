package tabby.analysis.model;


import com.google.common.collect.Sets;
import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;

import java.util.Arrays;
import java.util.Set;

/**
 * @project tabby_ng
 * @since 2025/7/10
 */
public class Hibernate3TplExecuteModel extends Model {

    private static String HIBERNATE3_EXECUTE = "java.lang.Object doInHibernate(org.hibernate.Session)";

    @Override
    boolean apply(InvokeExpr invokeExpr, MethodReference callee) {
        // org.springframework.orm.hibernate3.HibernateTemplate

        if("org.springframework.orm.hibernate3.HibernateTemplate".equals(callee.getClassname())
                && ("execute".equals(callee.getName()) || "executeFind".equals(callee.getName()))) {
            Set<String> types = cgBuilder.types.get(1); // 获取参数的类型

            for(String type : types){
                MethodReference methodRef = getMethodReference(type, HIBERNATE3_EXECUTE);
                if(methodRef != null){
                    needToReplace.add(methodRef);
                }
            }

            Set<Integer> pos = cgBuilder.positions.get(1);
            cgBuilder.positions.clear();
            cgBuilder.positions.add(pos);
            cgBuilder.positions.add(Sets.newHashSet(Arrays.asList(-3)));
            cgBuilder.types.clear();
            cgBuilder.types.add(types);
            cgBuilder.types.add(Sets.newHashSet("org.hibernate.Session"));
            cgBuilder.setInvokeType("ManualInvoke");
            cgBuilder.setCallerThisFieldObj(false);
            return true;
        }
        return false;
    }

}
