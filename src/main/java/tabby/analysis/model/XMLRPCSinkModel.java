package tabby.analysis.model;

import com.google.common.collect.Sets;
import soot.Value;
import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.PositionUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/12/07
 */
public class XMLRPCSinkModel extends Model {

    @Override
    boolean apply(InvokeExpr invokeExpr, MethodReference callee) {
        if ("org.xml.sax.XMLReader".equals(callee.getClassname())
                && "setContentHandler".equals(callee.getName())) {
            Value arg = invokeExpr.getArg(0);
            Set<String> objTypes = container.getVarNodeTypes(arg);

            if (objTypes.contains("org.apache.xmlrpc.parser.XmlRpcRequestParser")
                    || objTypes.contains("org.apache.xmlrpc.parser.XmlRpcResponseParser")) {

                MethodReference readObjectMethodRef
                        = dataContainer.getOrAddMethodRefBySubSignature("java.io.ObjectInputStream", "java.lang.Object readObject()");

                if (readObjectMethodRef != null) {
                    // 建立人工指向反序列化的边
                    List<Set<Integer>> positions = new LinkedList<>();
                    positions.add(Sets.newHashSet(PositionUtils.THIS));
                    List<Set<String>> types = new LinkedList<>();
                    types.add(Sets.newHashSet("java.io.ObjectInputStream"));

                    cgBuilder.setTypes(types);
                    cgBuilder.setPositions(positions);
                    cgBuilder.setInvokeType("ManualInvoke");
                    cgBuilder.setCallerThisFieldObj(false);

                    needToReplace.add(readObjectMethodRef);
                }
            }
        }
        return false;
    }
}
