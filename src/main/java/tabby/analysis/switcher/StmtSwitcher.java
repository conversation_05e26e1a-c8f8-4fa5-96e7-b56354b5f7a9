package tabby.analysis.switcher;


import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import soot.Local;
import soot.SootField;
import soot.Value;
import soot.jimple.*;
import tabby.analysis.container.ValueContainer;
import tabby.analysis.data.Context;
import tabby.analysis.data.Taint;
import tabby.analysis.data.nodes.*;
import tabby.common.bean.edge.Call;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 一个analysis只会生成一个
 * @project tabby_ng
 * @since 2025/4/1
 */
@Getter
@Slf4j
public class StmtSwitcher extends AbstractStmtSwitch {

    private Context context = null;
    private ValueContainer container = null;
    private ExprSwitcher exprSwitcher = null;
    private Set<Call> callEdges = new HashSet<>();
    private Multimap<String, String> actions = MultimapBuilder.hashKeys().hashSetValues().build();
    private Map<String, Integer> triggerTimes = null;

    public StmtSwitcher() {
        this.exprSwitcher = new ExprSwitcher();
    }

    public void accept(Context context){
        this.context = context;
        this.container = context.getContainer();
        this.exprSwitcher.accept(context);
        this.exprSwitcher.accept(callEdges);
    }

    public void accept(Stmt stmt){
        this.exprSwitcher.accept(stmt);
    }

    /**
     * a = @This
     * b = @Parameter
     * 正常这些语句都是在函数开始的地方，是最初的状态，所以不用进行field的处理
     * 绑定this和parameters
     *
     * @param stmt
     */
    @Override
    public void caseIdentityStmt(IdentityStmt stmt) {
        Value lop = stmt.getLeftOp();
        Value rop = stmt.getRightOp();

        Node obj = container.createNode((Local) lop);
        obj.getTaints().clear();
        if (rop instanceof ThisRef) {
            obj.getTaints().add(Taint.of(PositionUtils.THIS, "this"));
            context.getCurObjects()[0] = obj.getName();
            context.getCopiedCurObjects()[0] = obj.clone();
        } else if (rop instanceof ParameterRef) {
            int index = ((ParameterRef) rop).getIndex();
            obj.getTaints().add(Taint.of(index, "param-" + index));
            if (context.getCurObjects().length >= index + 1) {
                context.getCurObjects()[index + 1] = obj.getName();
                context.getCopiedCurObjects()[index + 1] = obj.clone();
            }
        }

        container.relateToVarTablesWithRemoval(obj.getName(), obj);
        container.save(obj);
    }

    /**
     * return obj
     * 返回值保存在context
     * 并且生成action
     *
     * @param stmt
     */
    @Override
    public void caseReturnStmt(ReturnStmt stmt) {
        Value value = stmt.getOp();
        Set<Node> retNodes = container.getNodes(value);
        caseReturn(retNodes);
    }

    @Override
    public void caseReturnVoidStmt(ReturnVoidStmt stmt) {
        caseReturn(null);
    }

    public void caseReturn(Set<Node> nodes){
        Multimap<String, Multimap<String, String>> summaries =
                MultimapBuilder.hashKeys().hashSetValues().build();
        // 先看看收集后保存，然后直接还原的方式可不可行
        // 收集完成后再进行聚合，有些不同的属性有污点可以尝试将其聚合起来看成一个对象
        // 保存 return nodes
        if(nodes != null && !nodes.isEmpty()){
            summaries.putAll(container.generateSummary("return", nodes));
        }

        // 保存 this、params
        for(int i=0; i < context.getCurObjects().length; i++){
            String key = "param-"+(i-1);
            if(i == 0){
                key = "this";
            }
            String varName = context.getCurObjects()[i];
            Node copied = context.getCopiedCurObjects()[i]; // 找到最初创建的param-n的node，不看变量应用的变化（var table，因为这是引用变化，变化不会体现在外部）
            if(copied != null){
                Set<Node> varNodes = container.getNodesByVarName(varName);
                for(Node node : varNodes){
                    if(node != null && !copied.equals(node)){
                        summaries.putAll(container.generateSummary(key, Arrays.asList(node)));
                    }
                }
            }
        }
        // store actions
        if(!summaries.isEmpty() && !context.getSummaries().isEmpty()){
            ValueContainer current = new ValueContainer(summaries, container.getDataContainer());
            ValueContainer previs = new ValueContainer(context.getSummaries(), container.getDataContainer());
            previs.union(current);
            context.getSummaries().clear();
            context.getSummaries().putAll(previs.generateSummaries());
        }else if(context.getSummaries().isEmpty()){
            context.getSummaries().putAll(summaries);
        }
    }

    public Multimap<String, String> getAllRelatedNodes(Node node, boolean isRemoveUntainted){
        Multimap<String, String> tempLocalNodes = MultimapBuilder.hashKeys().hashSetValues().build();
        Set<Node> allNodes = container.getAllRelatedNodes(node, isRemoveUntainted);
        allNodes.forEach(related -> {
            tempLocalNodes.putAll(related.getUid(), related.serialize());
        });
        return tempLocalNodes;
    }

    /**
     * 处理如下几种情况
     * new => a = new newarray
     * assign => a = b; a.f = b; a = b.f; 由于目前算法将field类型当作Object了，所以也归类到assign上
     * store =>  a[1] = b;
     * load =>  a = b[1];
     * cast => a = (A) b;
     * invoke => a = b.func();
     * 这里不区分静态和非静态的区别，实际的操作是相同的
     * 对于store和load 主要用于数组类型
     * 每次assign都会触发一次变量传播，对于普通的对象，array类型的传播不太一样
     *
     * @param stmt
     */
    @Override
    public void caseAssignStmt(AssignStmt stmt) {
        Value lop = stmt.getLeftOp();
        String lName = SemanticUtils.extractValueName(lop);
        // 强制停止措施，不得已的情况会触发（防止因为当前函数的分析陷入死循环）
        if (SemanticUtils.increaseAndCheck(lName, triggerTimes)) {
            log.debug("Method {} triggered force stop mechanism!", context.getMethodSignature());
            context.setForceStop(true);
            return;
        }
        // 优先处理 rop
        Value rop = stmt.getRightOp();
        exprSwitcher.setResult(null);
        rop.apply(exprSwitcher);
        Set<Node> result = (Set<Node>) exprSwitcher.getResult();
        if(isNullResult(result)) return; // rop 为找不到的话，不处理；一般出现在常量的处理中

        if(isNewOp(result)){ // 预处理result
            Optional<Node> optional = result.stream().findFirst();
            if(optional.isPresent()){
                NewNode newNode = (NewNode) optional.get();
                Set<Node> nodes = container.getOrCreateNode(lop);
                for(Node node : nodes){
                    node.setType(newNode.getType());
                    container.save(node); // 修改了更新一下
                }
                result.clear();
                result.addAll(nodes);
            }
        }else if(isIgnoreOp(result)){
            // just add lop
            IgnoreNode ignoreNode = (IgnoreNode) result.toArray()[0];
            Set<Node> nodes = container.getOrCreateNode(lop);
            if(ignoreNode.isNeedTransfer()){
                for(Node node : nodes){
                    node.getTaints().clear();
                    node.getTaints().addAll(ignoreNode.getTaints());
                }
            }
            result.clear();
            result.addAll(nodes);
        }

        String type = lop.getType().toString();
        for(Node node : result){
            if(node.isFake()){
                node.setType(type);
            }
        }

        // 判断 left op 类型
        if(lop instanceof Local || lop instanceof StaticFieldRef){
            // a = b 直接赋值
            // C.f = c 静态字段赋值
            String varName = SemanticUtils.extractVarName(lop);
            container.assign(varName, result, lop instanceof StaticFieldRef, true);
        }else if(lop instanceof InstanceFieldRef ifr){
            // InstanceFieldRef field store 类型，将base的field内容替换掉
            Value base = ifr.getBase();
            SootField field = SemanticUtils.getField(ifr);
            if(base != null && field != null){
                Set<Node> baseNodes = container.getOrCreateNode(base);
                for(Node baseNode: baseNodes){
                    if(baseNode == null || baseNode.isNull() || baseNode.isConstant()) continue;
                    baseNode.replaceFieldNodes(field.getName(), result, container);
                    container.save(baseNode);
                }
            }
        }else if(lop instanceof ArrayRef ar){
            // array[] array store 类型，将array的array字段添加新的NodeRef，这里只增不减
            Value base = ar.getBase();
            Set<Node> arrayNodes = container.getOrCreateNode(base);
            Set<NodeRef> refs = result.stream().map(Node::makeRef).collect(Collectors.toSet());
            for(Node arrayNode: arrayNodes){
                if(arrayNode instanceof ArrayNode an){
                    an.addArrayNode(refs, container, false);
                    container.save(arrayNode);
                }
            }
        }else {
            // 其他？一般不会有其他类型了，如果出现了需要分析完善一下代码
            log.info("{} type is {}, need to deal.", lop, lop.getType());
        }
    }

    /**
     * a.func
     *
     * @param stmt
     */
    @Override
    public void caseInvokeStmt(InvokeStmt stmt) {
        InvokeExpr ie = stmt.getInvokeExpr();
        ie.apply(exprSwitcher);
    }

    private boolean isNewOp(Set<Node> nodes){
        if(nodes == null) return false;
        for(Node node: nodes){ // 只有一个new
            if(node instanceof NewNode){
                return true;
            }
        }
        return false;
    }

    private boolean isIgnoreOp(Set<Node> nodes){
        if(nodes == null || nodes.isEmpty()) return true;
        for(Node node: nodes){ // 只有一个new
            if(node instanceof IgnoreNode){
                return true;
            }
        }
        return false;
    }

    private boolean isNullResult(Set<Node> nodes){
        if(nodes == null || nodes.isEmpty()) return true;
        boolean isNull = true;
        for(Node node: nodes){
            if(node != null){
                isNull = false;
            }
        }
        return isNull;
    }

    public void setTriggerTimes(Map<String, Integer> triggerTimes) {
        this.triggerTimes = triggerTimes;
    }
}
