spring.profiles.active=default
#debug=true
#logging.level.tabby=DEBUG
logging.level.org.springframework=ERROR
logging.level.org.springframework.data=OFF
logging.level.org.springframework.orm=OFF
logging.level.org.h2=OFF
logging.level.org.hibernate=OFF
logging.level.com.zaxxer=OFF
logging.level.tabby=INFO
logging.level.sun.rmi=ERROR
logging.level.org.apache.jasper=OFF
logging.level.org.neo4j=WARN
#logging.level.com.zaxxer.hikari=TRACE
spring.datasource.url=jdbc:h2:mem:tabby;MODE=MySQL;LOCK_MODE=3;DB_CLOSE_DELAY=-1;COMPRESS=TRUE;AUTO_RECONNECT=TRUE;PAGE_SIZE=1048576
#spring.datasource.url=jdbc:h2:file:./cache/tabby;MODE=MySQL;LOCK_MODE=3;DB_CLOSE_DELAY=-1;COMPRESS=TRUE;AUTO_RECONNECT=TRUE;PAGE_SIZE=1048576
#spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=org.h2.Driver
#spring.datasource.druid.min-idle=20
#spring.datasource.druid.max-active=50
#spring.datasource.druid.max-wait=600000
#spring.datasource.druid.initial-size=10
#spring.datasource.druid.validation-query=select 1
spring.datasource.hikari.keepalive-time=60000
spring.datasource.hikari.max-lifetime=3600000
spring.datasource.hikari.maximum-pool-size=60
spring.datasource.hikari.connection-timeout=600000
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.jdbc.batch_size=1000
spring.jpa.properties.hibernate.order_updates=true
#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true

